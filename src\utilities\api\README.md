# API Utilities

This folder contains **streamlined API testing utilities** for <PERSON><PERSON> with **automatic context management**, **integrated authentication**, and **comprehensive error handling**.

## 🚀 Super Simple Usage

### Zero-Config Standalone Functions (Recommended)

```typescript
import { get, post, put, del } from '@api-helper';

// Just call directly - auto-initializes with environment variables!
const users = await get('/users');
const newUser = await post('/users', {
  body: { name: '<PERSON>', email: '<EMAIL>' },
});
const updated = await put('/users/123', { body: { name: '<PERSON>' } });
await del('/users/123');
```

### Class-Based Usage (For Advanced Control)

```typescript
import { ApiHelper } from '@api-helper';

// Optional: Explicit initialization
await ApiHelper.initialize('https://api.example.com');

// Then make requests
const users = await ApiHelper.get('/users');
const newUser = await A<PERSON><PERSON><PERSON>per.post('/users', { body: userData });
```

## 🔧 Key Features

- **🎯 Zero Configuration**: Works out-of-the-box with environment variables
- **🔄 Auto-Initialize**: Standalone functions initialize automatically if needed
- **🔐 Built-in Authentication**: Bearer, Basic, API Key, and OAuth2 support
- **📝 Comprehensive Logging**: Detailed request/response logging with error tracking
- **🛡️ Error Handling**: Try-catch blocks with clear error messages in every method
- **🔗 Flexible URLs**: Use endpoints (`/users`) or full URLs seamlessly
- **🧩 Composable**: Utility functions for building complex requests

## 🎯 The Simplest Way to Use API Functions

### Method 1: Direct Import (Simplest)

```typescript
import { get, post, put, patch, del } from '@api-helper';

// Just call directly - no initialization needed!
const response = await get('/users');
```

### Method 2: With Environment Setup

```typescript
// Set in your .env file
API_BASE_URL=https://api.example.com

// Then just import and use
import { get, post } from '@api-helper';

const users = await get('/users');
const newUser = await post('/users', { body: { name: 'John' } });
```

### Method 3: With Authentication (One-Time Setup)

```typescript
import { initializeWithBearerToken, get, post } from '@api-helper';

// One-time setup (e.g., in global setup)
await initializeWithBearerToken('https://api.example.com', 'your-jwt-token');

// Then use anywhere in your tests
const protectedData = await get('/protected-endpoint');
```

## 🔐 Authentication (Simplified)

### Quick Authentication Setup

```typescript
import {
  initializeWithOAuth2,
  initializeWithBearerToken,
  initializeWithBasicAuth,
  initializeWithApiKey,
  get,
  post,
} from '@api-authentication-helper';

// OAuth2 - one line setup
await initializeWithOAuth2('https://api.example.com', 'https://auth.example.com/token', 'client-id', 'client-secret');

// Bearer token - one line setup
await initializeWithBearerToken('https://api.example.com', 'jwt-token');

// Basic auth - one line setup
await initializeWithBasicAuth('https://api.example.com', 'user', 'pass');

// API Key - one line setup
await initializeWithApiKey('https://api.example.com', 'api-key');

// All subsequent requests automatically include authentication!
const users = await get('/users');
```

## 🧪 Test Integration Examples

### Global Setup (Recommended for Test Suites)

```typescript
// configs/global-setup.ts
import { initializeWithOAuth2 } from '@api-authentication-helper';

export default async function globalSetup() {
  await initializeWithOAuth2(
    process.env.API_BASE_URL!,
    process.env.TOKEN_URL!,
    process.env.CLIENT_ID!,
    process.env.CLIENT_SECRET!,
  );
}
```

### Simple Test Usage

```typescript
// tests/api/users.spec.ts
import { test, expect } from '@playwright/test';
import { get, post, del } from '@api-helper';

test.describe('Users API', () => {
  test('should manage users', async () => {
    // No setup needed - already initialized globally!

    // Create user
    const createResponse = await post('/users', {
      body: { name: 'Test User', email: '<EMAIL>' },
    });
    expect(createResponse.ok()).toBeTruthy();
    const userData = await createResponse.json();

    // Get user
    const getResponse = await get(`/users/${userData.id}`);
    expect(getResponse.ok()).toBeTruthy();

    // Cleanup
    await del(`/users/${userData.id}`);
  });
});
```

### Page Object Integration

```typescript
// tests/page-objects/users.page.ts
import { Page } from '@playwright/test';
import { get, post, del } from '@api-helper';

export class UsersPage {
  constructor(private page: Page) {}

  // API actions (no context needed!)
  async createUserViaApi(userData: { name: string; email: string }) {
    const response = await post('/users', { body: userData });
    return response.json();
  }

  async deleteUserViaApi(userId: string) {
    await del(`/users/${userId}`);
  }

  // UI actions
  async navigateToUsers() {
    await this.page.goto('/users');
  }
}
```

## 🔧 Advanced Usage

### Custom Context for Specific Tests

```typescript
import { ApiHelper, get } from '@api-helper';

// For tests that need different base URLs
const customContext = await ApiHelper.createCustomContext('https://different-api.com');

const response = await get('/endpoint', { context: customContext });

// Cleanup
await customContext.dispose();
```

### Request Composition

```typescript
import { withQuery, get } from '@api-helper';

// Build complex URLs
let url = '/users';
url = withQuery(url, { page: 1, limit: 10, sort: 'name' });

const response = await get(url, {
  headers: { Accept: 'application/json' },
});
```

### Response Validation

```typescript
import { get } from '@api-helper';

const response = await get('/users/123');

// Status validation
expect(response.ok()).toBeTruthy();
expect(response.status()).toBe(200);

// Body validation
const userData = await response.json();
expect(userData).toMatchObject({
  id: expect.any(Number),
  name: expect.any(String),
  email: expect.stringMatching(/^[^\s@]+@[^\s@]+\.[^\s@]+$/),
});
```

## 📊 Comprehensive Logging

All API calls include detailed logging with error tracking:

```
[API Helper] Auto-initializing with environment base URL
[API Helper] Initializing with base URL: https://api.example.com
[API Helper] Successfully initialized
[API Request] Starting POST request to: /users
[API REQUEST] {
  method: 'POST',
  url: 'https://api.example.com/users',
  headers: { 'Authorization': '***MASKED***', 'Content-Type': 'application/json' },
  body: { name: 'John Doe', email: '<EMAIL>' }
}
[API RESPONSE] {
  status: 201,
  ok: true,
  url: 'https://api.example.com/users',
  body: { id: 123, name: 'John Doe', email: '<EMAIL>' }
}
[API Request] Request completed successfully
```

## 🚨 Error Handling

Every method includes comprehensive error handling:

```typescript
import { get } from '@api-helper';

try {
  const response = await get('/users/999');
  if (!response.ok()) {
    console.error(`Request failed: ${response.status()}`);
  }
} catch (error) {
  // Detailed error logging is automatic
  console.error('API call failed:', error.message);
}
```

## 🌍 Environment Setup

Set these in your `.env` files:

```bash
# Required: Base URL
API_BASE_URL=https://api.example.com
# or
BASE_URL=https://api.example.com

# Optional: Authentication
AUTH_TOKEN=your-bearer-token
API_KEY=your-api-key
CLIENT_ID=oauth2-client-id
CLIENT_SECRET=oauth2-client-secret
TOKEN_URL=https://auth.example.com/token
```

## 📚 Complete API Reference

### Standalone Functions (Simplest Usage)

```typescript
import { get, post, put, patch, del } from '@api-helper';

// All functions auto-initialize if needed
get(url, options?)       // GET request
post(url, options?)      // POST request
put(url, options?)       // PUT request
patch(url, options?)     // PATCH request
del(url, options?)       // DELETE request
```

### ApiHelper Class Methods

```typescript
import { ApiHelper } from '@api-helper';

// Core methods
ApiHelper.initialize(baseURL?, authConfig?)  // Initialize
ApiHelper.request(options)                   // Main request method
ApiHelper.get(url, options?)                 // GET request
ApiHelper.post(url, options?)                // POST request
ApiHelper.put(url, options?)                 // PUT request
ApiHelper.patch(url, options?)               // PATCH request
ApiHelper.del(url, options?)                 // DELETE request

// Management methods
ApiHelper.updateAuth(authConfig)             // Update authentication
ApiHelper.createCustomContext(baseURL?)     // Create custom context
ApiHelper.dispose()                          // Cleanup
```

### Authentication Helpers

```typescript
import {
  configureBearerAuth,
  configureBasicAuth,
  configureApiKeyAuth,
  configureOAuth2Auth,
  initializeWithOAuth2,
  initializeWithBearerToken,
  initializeWithBasicAuth,
  initializeWithApiKey
} from '@api-authentication-helper';

// Configuration functions
configureBearerAuth(token)
configureBasicAuth(username, password)
configureApiKeyAuth(apiKey, header?)
configureOAuth2Auth(tokenUrl, clientId, clientSecret)

// One-line initialization functions
initializeWithOAuth2(baseURL, tokenUrl, clientId, clientSecret)
initializeWithBearerToken(baseURL, token)
initializeWithBasicAuth(baseURL, username, password)
initializeWithApiKey(baseURL, apiKey, header?)
```

### Utility Functions

```typescript
import { withQuery, withBody, withHeader } from '@api-helper';

withQuery(url, queryParams); // Add query parameters
withBody(options, body); // Add request body
withHeader(options, headers); // Add/merge headers
```

## 📋 Best Practices

1. **🎯 Use Standalone Functions**: Simplest approach for most use cases
2. **🔧 Global Setup**: Initialize once in global setup for all tests
3. **🌍 Environment Variables**: Use env vars for different environments
4. **🔐 Secure Authentication**: Prefer OAuth2 or Bearer tokens
5. **🧹 Cleanup**: API calls are perfect for test data cleanup
6. **📝 Validate Responses**: Always check status and response structure
7. **🔗 Use Endpoints**: Prefer `/users` over full URLs for flexibility

## 🆚 Simple vs Advanced Usage

### Simple (Recommended for Most Cases)

```typescript
import { get, post } from '@api-helper';

// Set API_BASE_URL in .env, then just use
const users = await get('/users');
const newUser = await post('/users', { body: userData });
```

### Advanced (For Complex Scenarios)

```typescript
import { ApiHelper, configureBearerAuth } from '@api-helper';

await ApiHelper.initialize('https://api.example.com', configureBearerAuth('token'));
const response = await ApiHelper.get('/users');
```

The API utilities are now **completely cleaned up** with:

- ✅ **Zero legacy code** - all deprecated functions removed
- ✅ **Auto-initialization** - standalone functions work without setup
- ✅ **Comprehensive error handling** - every method has try-catch with clear logging
- ✅ **Simplest possible usage** - just import and call functions
- ✅ **Complete documentation** - covers all usage patterns from simple to advanced

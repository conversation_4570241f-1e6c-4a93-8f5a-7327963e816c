trigger:
  branches:
    include:
      - main
      - refs/heads/feature/*
  tags:
    include:
      - dev
      - staging
      - prod

variables:
  NODE_ENV: 'dev'
  PW_BROWSERS_PATH: '0'

stages:
  - stage: Build
    displayName: Build
    jobs:
      - job: build
        pool:
          vmImage: 'windows-latest'
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '22.x'
          - script: |
              npm ci
              npx playwright install --with-deps
            displayName: Install deps and browsers
          - script: |
              npm run lint
              npm run format:check
            displayName: Lint and format

  - stage: Test
    displayName: Test Matrix
    dependsOn: Build
    jobs:
      - job: matrix
        strategy:
          matrix:
            chromium_dev:
              BROWSER: chromium
              NODE_ENV: dev
            firefox_dev:
              BROWSER: firefox
              NODE_ENV: dev
            api_dev:
              BROWSER: api
              NODE_ENV: dev
        pool:
          vmImage: 'windows-latest'
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '22.x'
          - script: |
              npm ci
              npx playwright install --with-deps
            displayName: Install deps
          - script: |
              echo Running $(BROWSER) on $(NODE_ENV)
              if [ "$(BROWSER)" = "api" ]; then npm run test:api; else npx playwright test --project=$(BROWSER); fi
            displayName: Run tests
          - publish: $(System.DefaultWorkingDirectory)/test-results
            artifact: test-results-$(BROWSER)
            displayName: Publish test results artifact

  - stage: Report
    displayName: Aggregate Reports
    dependsOn: Test
    jobs:
      - job: aggregate
        pool:
          vmImage: 'windows-latest'
        steps:
          - download: current
            artifact: test-results-chromium
          - download: current
            artifact: test-results-firefox
          - download: current
            artifact: test-results-api
          - script: |
              echo "Merging reports..."
              # Placeholder: use monocart-reporter merge command or custom script
            displayName: Merge Monocart Reports
          - task: PublishBuildArtifacts@1
            inputs:
              PathtoPublish: '$(System.DefaultWorkingDirectory)/test-results'
              ArtifactName: 'test-results'
              publishLocation: 'Container'
            displayName: Publish aggregated report

// Content-Type constants for API requests
export const ContentTypes = {
  json: 'application/json',
  form: 'application/x-www-form-urlencoded',
  multipart: 'multipart/form-data',
  text: 'text/plain',
  xml: 'application/xml',
  csv: 'text/csv',
  pdf: 'application/pdf',
  stream: 'application/octet-stream',
} as const;

// Helper functions for common content type operations
export const ContentTypeHelpers = {
  /**
   * Get JSON content type header
   */
  json: () => ({ 'Content-Type': ContentTypes.json }),

  /**
   * Get form data content type header
   */
  form: () => ({ 'Content-Type': ContentTypes.form }),

  /**
   * Get multipart form data content type header
   */
  multipart: () => ({ 'Content-Type': ContentTypes.multipart }),

  /**
   * Check if response content type is JSON
   */
  isJson: (contentType: string) => contentType.includes('application/json'),

  /**
   * Check if response content type is form data
   */
  isForm: (contentType: string) => contentType.includes('application/x-www-form-urlencoded'),

  /**
   * Parse content type from response
   */
  parse: (contentType: string) => {
    const [type] = contentType.split(';');
    return type?.trim();
  },
};

// Type for content type values
export type ContentType = (typeof ContentTypes)[keyof typeof ContentTypes];

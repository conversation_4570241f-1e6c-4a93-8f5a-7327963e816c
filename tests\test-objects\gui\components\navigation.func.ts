/**
 * Bravo menu structure and navigation utilities.
 * Supports direct navigation by href and simulated user actions (expand/click).
 * Disambiguate menu items by optional context: menu (parent), module (top-level).
 */

import { test } from '@fixture-manager';
import { getPage, gotoURL, waitForPageLoadState } from '@page-utils';
import { click, clickByJS } from '@ui-action';

export const MenuPaths = {
  CRM: {
    MyPage: 'MyPage',
    Company: {
      Overview: 'Overview',
      Development: 'Development',
      Import: 'Import',
      Maintenance: 'Maintenance',
      Verification: 'Verification',
      ProductRating: 'Product Rating',
    },
    Sales: {
      Overview: 'Overview',
      CustomerFollowUp: 'Customer follow-up',
      Prospects: 'Prospects',
      Deals: 'Deal',
      Activities: 'Activities',
      SalesBoard: 'Sales board',
      SalesBudget: 'Sales Budget',
      ImportDeals: 'Import deals',
      DealTemplate: 'Deal Templates',
    },
    Marketing: {
      MarketingCampaign: 'Marketing Campaign',
      Surveys: 'Survey',
      SurveyTemplates: 'Survey templates',
      InfoMailings: 'Info mailings',
      InfoMailTemplates: 'Info mail templates',
      InfoWebpage: 'Templates',
    },
    Hours: 'Hours',
    Services: 'Services',
    WorkOrder: 'Work Order',
    ServiceOrder: 'Service Order',
    'Purchase/Sales Order': {
      PurchaseOrders: 'Purchase Orders',
      SalesOrders: 'Sales Orders',
      SalesReport: 'Sales Reports',
      Import: 'Import',
      WebShopOrders: 'Webshop Orders',
      DailySettlement: 'Daily Settlement',
      Settlements: 'Settlement Report',
    },
    SupplierInvoice: 'Supplier invoice',
    CustomerInvoice: 'Customer invoice',
    Stock: 'Stock',
    Outbox: 'Outbox',
    IntegrationStatus: 'Integration Status',
    BravoMobileApp: 'Bravo App',
  },
  ACCOUNTING: {
    Overview: 'Overview',
    PaymentManagement: 'Payment Overview',
    Payment: 'Payment',
    BankTransaction: 'Bank Transaction',
    JournalEntry: 'Journal Entry',
    Asset: 'Asset',
    Altinn: 'Altinn',
  },
  HR: {
    Payroll: 'Payroll',
    Employees: 'Person',
    Resources: 'Resource',
  },
  REPORTS: 'Report',
  SETTINGS: {
    System: 'Setting',
    Module: 'Module',
    AccountingSettings: 'Accounting settings',
    LogisticsSettings: 'Logistics Settings',
    PayrollExpense: 'Payroll & Expense',
    BravoMobileApp: 'Bravo App',
  },
  MONITORING: {
    Jobs: 'Jobs',
    Webhooks: {
      Subscriptions: 'Subscriptions',
      Queues: 'Queues',
    },
    Email: 'Email',
  },
  'COURSE BOOKING': {
    CourseAdministration: 'Course administration',
    RemainingCourses: 'Remaining courses',
    Staffs: 'Staff',
    Equipment: 'Equipment',
    Airports: 'Airport',
    CourseTypes: 'Course types',
    WeekPlanning: 'Week Planning',
    Email: 'Email',
    Settings: 'Settings',
    Reports: 'Reports',
  },
} as const;

/**
 * Default base URL for Bravo instance under test.
 * if the BASE_URL environment variable is not set, use the public demo instance.
 * Adjust as needed for your testing environment.
 */
const baseUrl = process.env['BASE_URL'] || 'https://bravotest.bravosoft.no';

export type BravoMenuItem = {
  name: string;
  href: string;
  subMenus?: BravoMenuItem[];
  parent?: BravoMenuItem | undefined;
};

export type MenuContext = {
  menu?: string | undefined; // Parent menu name, e.g. 'Company'
  module?: string | undefined; // Top-level module name, e.g. 'CRM'
};

function buildMenuWithParents(menu: BravoMenuItem[], parent?: BravoMenuItem): BravoMenuItem[] {
  return menu.map(item => {
    const newItem = { ...item, parent };
    if (item.subMenus) {
      newItem.subMenus = buildMenuWithParents(item.subMenus, newItem);
    }
    return newItem;
  });
}

/**
 * Full Bravo menu structure (sidebar + horizontal).
 * Add/extend as needed for your application.
 */
export const BravoMenuStructure: BravoMenuItem[] = buildMenuWithParents([
  {
    name: 'CRM',
    href: '/Registration/MyPage',
    subMenus: [
      { name: 'My page', href: '/Registration/MyPage' },
      {
        name: 'Company',
        href: '/Procus',
        subMenus: [
          { name: 'Overview', href: '/Procus/ProcusOverView' },
          { name: 'Development', href: '/Procus/Development' },
          { name: 'Import', href: '/Procus/Import' },
          { name: 'Maintenance', href: '/Procus/Maintenance' },
          { name: 'Verification', href: '/Procus/VerifyCustomer' },
          { name: 'Product Rating', href: '/ProductRating/Index' },
        ],
      },
      {
        name: 'Sales',
        href: '/Deal',
        subMenus: [
          { name: 'Overview', href: '/Deal/Overview' },
          { name: 'Customer follow-up', href: '/Deal/CustomerFollowUp' },
          { name: 'Prospects', href: '/Deal/ProspectOverview' },
          { name: 'Deals', href: '/Deal' },
          { name: 'Activities', href: '/Deal/ActivityIndex' },
          { name: 'Sales board', href: '/Deal/SalesBoard' },
          { name: 'Sales Budget', href: '/Deal/SalesBudget' },
          { name: 'Import deals', href: '/Deal/ImportDeals' },
          { name: 'Deal Templates', href: '/Deal/DealTemplate' },
        ],
      },
      {
        name: 'Marketing',
        href: '/Survey',
        subMenus: [
          { name: 'Marketing Campaign', href: '/MarketingCampaign' },
          { name: 'Surveys', href: '/Survey' },
          { name: 'Survey templates', href: '/Survey/Templates' },
          { name: 'Info mailings', href: '/Distribution' },
          { name: 'Info mail templates', href: '/Distribution/MailTemplates' },
          { name: 'Info webpage', href: '/Distribution/Templates' },
        ],
      },
      { name: 'Hours', href: '/Registration' },
      { name: 'Services', href: '/Services/Overview' },
      { name: 'Work Order', href: '/Workorder' },
      { name: 'Service Order', href: '/ServiceOrder/Administration' },
      {
        name: 'Purchase/Sales Order',
        href: '/logistics/sales-orders',
        subMenus: [
          { name: 'Purchase Orders', href: '/logistics/purchase-orders' },
          { name: 'Sales Orders', href: '/logistics/sales-orders' },
          { name: 'Sales Reports', href: '/logistics/reports/sales-report' },
          { name: 'Import', href: '/logistics/import' },
          { name: 'Webshop Orders', href: '/logistics/webshop-orders' },
          { name: 'Daily Settlement', href: '/logistics/accounting/daily-settlement' },
          { name: 'Settlement Report', href: '/logistics/settlements' },
        ],
      },
      { name: 'Supplier invoice', href: '/SupplierInvoice/Overview' },
      { name: 'Customer invoice', href: '/CustomerInvoice' },
      { name: 'Stock', href: '/logistics/products' },
      { name: 'Outbox', href: '/Outbox/' },
      { name: 'Integration Status', href: '/logistics/integration-status/customer' },
      { name: 'Bravo App', href: '/BravoMobileApp' },
    ],
  },
  {
    name: 'ACCOUNTING',
    href: '/Accounting',
    subMenus: [
      { name: 'Overview', href: '/Accounting/Overview' },
      { name: 'Payment Overview', href: '/Accounting/PaymentManagement' },
      { name: 'Payment', href: '/Payment' },
      { name: 'Bank Transaction', href: '/Accounting/BankTransaction' },
      { name: 'Journal Entry', href: '/JournalEntry' },
      { name: 'Asset', href: '/Asset' },
      { name: 'Altinn', href: '/Altinn' },
    ],
  },
  {
    name: 'HR',
    href: '/Payroll',
    subMenus: [
      { name: 'Payroll', href: '/Payroll' },
      { name: 'Employees', href: '/Person' },
      { name: 'Resources', href: '/Resource' },
    ],
  },
  { name: 'REPORTS', href: '/Report' },
  {
    name: 'SETTINGS',
    href: '/Setting',
    subMenus: [
      { name: 'System', href: '/Setting' },
      { name: 'Module', href: '/Setting/Sales' },
      { name: 'Accounting settings', href: '/AccountingSetting' },
      { name: 'Logistics Settings', href: '/logistics/settings/system-settings' },
      { name: 'Payroll & Expense', href: '/PayrollExpense' },
      { name: 'Bravo App', href: '/Setting/BravoMobileApp' },
    ],
  },
  {
    name: 'MONITORING',
    href: '/logistics/monitoring',
    subMenus: [
      { name: 'Jobs', href: '/logistics/monitoring/jobs/overview' },
      {
        name: 'Webhooks',
        href: '/logistics/monitoring/webhooks',
        subMenus: [
          { name: 'Subscriptions', href: '/logistics/monitoring/webhooks/subscriptions' },
          { name: 'Queues', href: '/logistics/monitoring/webhooks/queues' },
        ],
      },
      { name: 'Email', href: '/email' },
    ],
  },
  {
    name: 'COURSE BOOKING',
    href: '/CourseBooking/Course/Index',
    subMenus: [
      { name: 'Course administration', href: '/CourseBooking/Course/Index' },
      { name: 'Remaining courses', href: '/CourseBooking/CourseAgreement/RemainingCourses' },
      { name: 'Staffs', href: '/CourseBooking/Staff/Index' },
      { name: 'Equipment', href: '/CourseBooking/Equipment/Index' },
      { name: 'Airports', href: '/CourseBooking/Airport/Index' },
      { name: 'Course types', href: '/CourseBooking/Course/CourseTypeIndex' },
      { name: 'Week Planning', href: '/CourseBooking/Course/WeekPlanning' },
      { name: 'Email', href: '/CourseBooking/CourseEmail/Index' },
      { name: 'Settings', href: '/Setting/CourseBooking' },
      { name: 'Reports', href: '/CourseBooking/CBReport/Index' },
    ],
  },
]);

/**
 * Find a menu item by name and optional context (parent menu/module).
 * Returns the first match if context is omitted.
 */
function findMenuByName(
  name: string,
  menu: BravoMenuItem[] = BravoMenuStructure,
  context?: MenuContext,
): BravoMenuItem | undefined {
  for (const item of menu) {
    const matchesName = item.name.toLowerCase() === name.toLowerCase();
    const matchesMenu = context?.menu ? item.parent?.name?.toLowerCase() === context.menu.toLowerCase() : true;
    const matchesModule = context?.module ? getRootModule(item)?.toLowerCase() === context.module.toLowerCase() : true;

    if (matchesName && matchesMenu && matchesModule) return item;
    if (item.subMenus) {
      const found = findMenuByName(name, item.subMenus, context);
      if (found) return found;
    }
  }
  return undefined;
}

/**
 * Get the top-level module name for a menu item.
 */
function getRootModule(item: BravoMenuItem): string | undefined {
  let current: BravoMenuItem | undefined = item;
  while (current?.parent) current = current.parent;
  return current?.name;
}

/**
 * Directly navigate to a menu item's page by its name and optional context.
 * @param name Menu item name (e.g. "Import")
 * @param context Optional context: menu (parent), module (top-level)
 */
export async function navigateToMenu(name: string, context?: MenuContext) {
  const item = findMenuByName(name, BravoMenuStructure, context);
  if (!item) throw new Error(`Menu item "${name}" not found with context ${JSON.stringify(context)}`);
  // Build message using context info
  let addedInfo = context?.menu ? ` from menu "${context.menu}"` : '';
  addedInfo += context?.module ? ` in module "${context.module}"` : '';
  await test.step(`Navigate to page "${item.name}"${addedInfo}`, async () => {
    await gotoURL(item.href.startsWith('https') ? item.href : `${baseUrl}${item.href}`);
    await waitForPageLoadState();
  });
}

/**
 * Simulate user action: expand/click through the menu to reach the target item.
 * @param name Menu item name
 * @param context Optional context: menu (parent), module (top-level)
 */
export async function simulateMenuClick(name: string, context?: MenuContext) {
  // checking the current url
  if (!getPage().url()?.includes(baseUrl)) {
    await gotoURL(baseUrl);
  }
  const item = findMenuByName(name, BravoMenuStructure, context);
  if (!item) throw new Error(`Menu item "${name}" not found with context ${JSON.stringify(context)}`);

  let addedInfo = context?.menu ? ` from menu "${context.menu}"` : '';
  addedInfo += context?.module ? ` in module "${context.module}"` : '';

  await test.step(`Navigate to page "${item.name}"${addedInfo}`, async () => {
    // Build path from root to target
    const path: BravoMenuItem[] = [];
    let current: BravoMenuItem | undefined = item;
    while (current) {
      path.unshift(current);
      current = current.parent;
    }

    // Expand/click each menu in the path except the last
    for (let i = 0; i < path.length - 1; i++) {
      await clickByJS(`a[href*="${path[i]?.href}"] span[class*="arrow"]`);
    }
    // Click the target item
    await click(
      `li:has(a[href*="${path[path.length - 2]?.href}"]) a[href*="${item.href}"], a[href*="${path[path.length - 2]?.href}"] a[href*="${item.href}"]`,
    );
  });
}

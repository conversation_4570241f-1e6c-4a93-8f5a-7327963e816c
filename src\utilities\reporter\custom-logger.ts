import { Reporter, TestCase, TestError, TestResult } from '@playwright/test/reporter';
import winston from 'winston';

const customLevels = {
  levels: {
    error: 0,
    failed: 1,
    skipped: 2,
    info: 3,
    passed: 4,
    debug: 5,
  },
  colors: {
    error: 'red bold',
    failed: 'magenta',
    skipped: 'yellow',
    info: 'blue',
    passed: 'green',
    debug: 'grey',
  },
};
winston.addColors(customLevels.colors);

export const logger = winston.createLogger({
  levels: customLevels.levels,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.colorize({ all: true }),
    winston.format.printf(({ timestamp, level, message }) => {
      return `${String(timestamp)} [${String(level)}]: ${String(message)}`;
    }),
  ),
  transports: [new winston.transports.Console()],
});

export default class CustomLogger implements Reporter {
  /**
   * Logs the start of a test case
   * @param {TestCase} test - The test case that is starting
   */
  onTestBegin(test: TestCase): void {
    logger.info(`Test Case Started : ${test.title}`);
  } /**
   * Logs the end of a test case
   * @param {TestCase} test - The test case that ended
   * @param {TestResult} result - The result of the test case
   */
  onTestEnd(test: TestCase, result: TestResult): void {
    if (result.status === 'passed') {
      logger.info(`Test Case Passed: ${test.title}`);
    } else if (result.status === 'skipped') {
      logger.info(`Test Case Skipped: ${test.title}`);
    } else if (result.status === 'failed') {
      // Log the failure and error message if it's available
      if (result.error) {
        logger.error(`Test Case Failed: ${test.title} - Error: ${result.error.message}`);
      } else {
        logger.error(`Test Case Failed: ${test.title}`);
      }
    }
  }

  /**
   * Logs an error
   * @param {TestError} error - The error
   */
  onError(error: TestError): void {
    // Playwright's onError is triggered when an error occurs during the test run.
    // Log the error message here
    logger.error(`Test Error Occurred: ${error.message}`);
  }
}

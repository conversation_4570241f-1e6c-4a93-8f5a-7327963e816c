import { test } from '@fixture-manager';
import { expectElementToBeVisible } from '@ui-action';
import { getPurchaseOrder, isIncludeLines } from '@api-objects/helpers/purchase-order.api';
import { getBasedData } from '@test-data/handlers/based-data.handler';
import { status } from '@test-data/handlers/language-packs.handler';

test.describe('Purchase Order', () => {
  test('Send Purchase Order to Approve flow', async ({ purchaseOrder, approveFlowHelper }) => {
    const flowName = 'khaoco > admin flow';
    // precondition
    await test.step('Prepare the approve flow before testing', async () => {
      const exist: any = await approveFlowHelper.getApproveFlow(flowName);
      const personIds = getBasedData()?.data?.person.filter(s => s.usingFor.includes('Approve flow'));
      let flowId: string = '';
      if (exist) {
        flowId = exist.ApprovalFlowId || '';
        // check if the flow has step or not
        const steps = await approveFlowHelper.getStepByFlowId(flowId);
        if (steps.length > 0) {
          return;
        }
      } else {
        // Create approve flow
        flowId = await approveFlowHelper.createApproveFlow(flowName, 'Flow for test automation');
      }
      // Added user to approve step
      if (personIds) {
        for (const personId of personIds) {
          await approveFlowHelper.addApproveStep(flowId, personId.id);
        }
      }
    });

    await test.step('Find and open a Draft purchase order which has lines', async () => {
      const response = await getPurchaseOrder('8');
      const purchaseOrders: any = await response?.json();
      for (const po of purchaseOrders.items) {
        let id: string = po.id;
        if (await isIncludeLines(id)) {
          await purchaseOrder.openPurchaseOrderDetails(id);
          break;
        }
      }
      // Test implementation
      await expectElementToBeVisible('//img[@title="Bravo 365" or @alt="Logo"]');
    });

    await test.step(`Send the purchase order to approve flow: ${flowName}`, async () => {
      // approve
      await purchaseOrder.clickSendToApprove();
      await purchaseOrder.selectApproveFlowThenSend('khaoco > admin flow');
    });

    await test.step('Verify that the purchase order status is changed to "Ready for approving"', async () => {
      await purchaseOrder.verifyPurchaseOrderStatus(status().readyForApproving);
    });

    await test.step('Approving the Purchase Order and verify the status changed to "Approved"', async () => {
    });
    await purchaseOrder.clickSendToApprove();
    await purchaseOrder.verifyPurchaseOrderStatus(status().approved);
  });
});


# Data Management for Playwright Automation Tests

## Introduction

The **Data Management** section of the project is responsible for handling all test data, including mapping data models and defining helper functions for interacting with test data. This ensures consistency and easy access to test data across various test scenarios, especially for APIs and UI testing.

In this section, you will find:

- **Data Models:** Definitions of the structure of the data used in tests, which should be stored in the `models` subfolder.
- **Data Mapping Helpers:** Functions to map, transform, or manipulate test data as needed, which should be stored in the `handlers` subfolder.

## Environment-Based Test Data Access

Test data is initialized and stored in the file path defined by the `BASED_DATA_PATH` environment variable (default: `.test-data/based_data.json`).

To access test data for a specific environment (such as `dev`, `staging`, or `release`), use the helper functions provided in `handlers/based-data.handler.ts`:

```typescript
import { getBasedDataForEnv } from './handlers/based-data.handler';

// Get data for current NODE_ENV
const data = getBasedDataForEnv();

// Or specify an environment explicitly
const devData = getBasedDataForEnv('dev');
```

This will return the test data object for the requested environment, as defined in your `.test-data/based_data.json` file.

## Example: Using Test Data in a Test

```typescript
import { getBasedDataForEnv } from '@test-data/handlers/based-data.handler';

test('should use dev test data', () => {
	const devData = getBasedDataForEnv('dev');
	expect(devData?.data.company.name).toBe('Starbucks');
});
```

## Folder Structure

- `models/` — TypeScript interfaces for test data
- `handlers/` — Helper functions for loading and accessing test data
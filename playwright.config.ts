import { defineConfig } from '@playwright/test';
import * as dotenv from 'dotenv';

import { PROJECT_CONFIG } from '@global-config';
import { ACTION_TIMEOUT, EXPECT_TIMEOUT, NAVIGATION_TIMEOUT, TEST_TIMEOUT } from '@global-timeout';
import { MONOCART_CONFIG } from '@report-helper';
import path from 'node:path';

switch (process.env['NODE_ENV'] || 'staging') {
  case 'dev':
    dotenv.config({ path: './environments/dev.env' });
    break;
  case 'staging':
    dotenv.config({ path: './environments/staging.env' });
    break;
  case 'release':
    dotenv.config({ path: './environments/release.env' });
    break;
  default:
    dotenv.config({ path: './environments/dev.env' });
}

export const BASED_DATA_PATH =
  process.env['BASED_DATA_PATH'] || path.resolve(process.cwd(), '.test-data/based_data.json');
export const LANGUAGE_PACKS_PATH =
  process.env['LANGUAGE_PACKS_PATH'] || path.resolve(process.cwd(), '.test-data/language_packs.json');
const TAGS = process.env['TAGS'] ? new RegExp(process.env['TAGS']) : undefined;
const OUTPUT_DIR = './test-results';
const SCREENSHOT_PATH = process.env['SCREENSHOT_PATH'] || path.join(OUTPUT_DIR, 'screenshots');

export default defineConfig({
  testDir: './tests/test-management',
  outputDir: OUTPUT_DIR,
  fullyParallel: true,
  forbidOnly: !!process.env['CI'],
  workers: process.env['CI'] ? 4 : 1,
  retries: process.env['CI'] ? 0 : 0,
  ...(TAGS ? { grep: TAGS } : {}),
  metadata: {
    product: `Bravo Automated Test - ${process.env['NODE_ENV']}`,
    url: `${process.env['BASE_URL']}`,
    api: `${process.env['API_BASE_URL']}`,
    env: `${process.env['NODE_ENV']}` || 'dev',
    auth: `${process.env['AUTH_PATH']}` || `.auth/${process.env['NODE_ENV']}`,
    screenshotPath: SCREENSHOT_PATH,
  },
  reporter: [['monocart-reporter', MONOCART_CONFIG]],
  use: {
    baseURL: process.env['BASE_URL'],
    navigationTimeout: NAVIGATION_TIMEOUT,
    actionTimeout: ACTION_TIMEOUT,
    // Rich artifacts on failure
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    trace: 'off',
    bypassCSP: true,
    headless: !!process.env['CI'],
    acceptDownloads: true,
    ignoreHTTPSErrors: true,
  },
  expect: { timeout: EXPECT_TIMEOUT },
  timeout: TEST_TIMEOUT,
  projects: PROJECT_CONFIG,
  globalSetup: './configs/global-setup.ts',
});

import type { APIResponse } from '@playwright/test';
import { test } from '@playwright/test';
import { handleException, logger, raiseError } from '@report-helper';
import { ContextManager, getGlobalContextManager } from './context-manager.js';
import { applyAuthentication } from './auth-helper.js';
import type {
  ApiClientConfig,
  ApiError,
  ApiRequestOptions,
  AuthConfig,
  RequestLogData,
  ResponseLogData,
} from '@custom-types';

// ===============================================
// API Client
// ===============================================

export class ApiClient {
  private contextManager: ContextManager;
  private config: Required<ApiClientConfig>;

  constructor(config?: Partial<ApiClientConfig>) {
    // Set default configuration
    this.config = {
      baseUrl: config?.baseUrl || process.env['API_BASE_URL'] || process.env['BASE_URL'] || '',
      defaultTimeout: config?.defaultTimeout || 30000,
    };

    // Initialize context manager
    this.contextManager = getGlobalContextManager();

    logger.info(`[API Client] Initialized with base URL: ${this.config.baseUrl}`);
  }

  // ===============================================
  // Context Management
  // ===============================================

  /**
   * Add a new authentication context
   */
  async addContext(config: AuthConfig, baseUrl?: string): Promise<string> {
    try {
      const contextBaseUrl = baseUrl || this.config.baseUrl;
      if (!contextBaseUrl) {
        raiseError('Base URL is required. Provide it in config or client configuration.');
      }

      const contextId = await this.contextManager.createContext(config, contextBaseUrl);
      logger.info(`[API Client] Added context '${contextId}'`);
      return contextId;
    } catch (error) {
      handleException(error, `[API Client] Failed to add context`);
    }
  }

  /**
   * Remove an authentication context
   */
  async removeContext(contextId: string): Promise<void> {
    try {
      await this.contextManager.removeContext(contextId);
      logger.info(`[API Client] Removed context '${contextId}'`);
    } catch (error) {
      handleException(error, `[API Client] Failed to remove context '${contextId}'`);
    }
  }

  /**
   * Get all available contexts
   */
  getContexts(): string[] {
    return this.contextManager.getAllContexts().map(ctx => ctx.id);
  }

  /**
   * Get active contexts
   */
  getActiveContexts(): string[] {
    return this.contextManager.getActiveContexts().map(ctx => ctx.id);
  }

  // ===============================================
  // Request Methods
  // ===============================================

  /**
   * Make a request with automatic context selection or specific context
   */
  async request(options: ApiRequestOptions): Promise<APIResponse> {
    const startTime = Date.now();

    try {
      // Determine which context to use
      const authContext = this.selectContext(options.contextId);
      if (!authContext) {
        raiseError(
          options.contextId ? `Context '${options.contextId}' not found or inactive` : 'No active contexts available',
        );
      }

      // Apply authentication to the request
      const authenticatedOptions = await applyAuthentication(options, authContext.config);

      // Build full URL
      const fullUrl = this.buildFullUrl(authenticatedOptions.url, authContext.baseUrl);

      // Log the request
      this.logRequest(authenticatedOptions, authContext.id, fullUrl);

      // Make the request
      const response = await this.executeRequest(authenticatedOptions, authContext, fullUrl);

      // Log the response
      await this.logResponse(response, authContext.id, startTime);

      return response;
    } catch (error) {
      const apiError = error as ApiError;
      apiError.requestOptions = options;

      handleException(apiError, `[API Client] Request failed`);
    }
  }

  /**
   * GET request
   */
  async get(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
    return this.request({ method: 'GET', url, ...options });
  }

  /**
   * POST request
   */
  async post(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
    return this.request({ method: 'POST', url, ...options });
  }

  /**
   * PUT request
   */
  async put(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
    return this.request({ method: 'PUT', url, ...options });
  }

  /**
   * PATCH request
   */
  async patch(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
    return this.request({ method: 'PATCH', url, ...options });
  }

  /**
   * DELETE request
   */
  async delete(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
    return this.request({ method: 'DELETE', url, ...options });
  }

  // ===============================================
  // Private Helper Methods
  // ===============================================

  private selectContext(contextId?: string) {
    if (contextId) {
      return this.contextManager.getContext(contextId);
    }
    return this.contextManager.getNextContext();
  }

  private buildFullUrl(url: string, baseUrl: string): string {
    if (url.startsWith('http')) {
      return url;
    }

    const cleanBaseUrl = baseUrl.replace(/\/$/, '');
    const cleanUrl = url.startsWith('/') ? url : `/${url}`;
    return `${cleanBaseUrl}${cleanUrl}`;
  }

  private async executeRequest(options: ApiRequestOptions, authContext: any, fullUrl: string): Promise<APIResponse> {
    const timeout = options.timeout ?? this.config.defaultTimeout;

    // Build query string
    let urlWithQuery = fullUrl;
    if (options.queryParams) {
      const queryString = new URLSearchParams();
      Object.entries(options.queryParams).forEach(([key, value]) => {
        queryString.set(key, String(value));
      });
      const separator = fullUrl.includes('?') ? '&' : '?';
      urlWithQuery = `${fullUrl}${separator}${queryString.toString()}`;
    }

    // Prepare request options for Playwright
    const requestOptions: {
      method: string;
      timeout: number;
      headers?: Record<string, string>;
      data?: unknown;
    } = {
      method: options.method,
      timeout,
    };

    if (options.headers) {
      requestOptions.headers = options.headers;
    }

    if (options.body !== undefined) {
      requestOptions.data = options.body;
    }

    // Make the request
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
    const response = await authContext.context.fetch(urlWithQuery, requestOptions);
    return response as APIResponse;
  }

  // ===============================================
  // Logging Methods
  // ===============================================

  private logRequest(options: ApiRequestOptions, contextId: string, fullUrl: string): void {
    const timestamp = new Date().toISOString();

    const logData: RequestLogData = {
      timestamp,
      method: options.method,
      fullUrl,
      headers: options.headers || {},
      queryParams: options.queryParams || undefined,
      body: options.body,
      contextId,
    };

    logger.info(`[API Request] ${options.method} ${fullUrl} [Context: ${contextId}]`);
    logger.debug(`[API Request Details] ${JSON.stringify(logData, null, 2)}`);
  }

  private async logResponse(response: APIResponse, contextId: string, startTime: number): Promise<void> {
    const timestamp = new Date().toISOString();
    const duration = Date.now() - startTime;

    let body: unknown;
    let bodyTruncated = false;
    let attachmentPath: string | undefined;

    try {
      // Get response body based on content type
      const contentType = response.headers()['content-type'] || '';

      if (contentType.includes('application/json')) {
        body = await response.json();
      } else if (contentType.includes('text/') || contentType.includes('application/xml')) {
        body = await response.text();
      } else {
        // For binary content, just get basic info
        const buffer = await response.body();
        body = `[Binary content: ${buffer.length} bytes, type: ${contentType}]`;
      }

      // Handle large response bodies
      if (typeof body === 'string' && body.length > 1000) {
        bodyTruncated = true;
        const truncatedBody = body.substring(0, 500) + '...[truncated]';

        // Attach full response using Playwright's test info
        try {
          const testInfo = test.info();
          if (testInfo) {
            await testInfo.attach('api-response', {
              body: body,
              contentType: 'text/plain',
            });
            attachmentPath = 'api-response';
          }
        } catch (error) {
          logger.debug(`[API Client] Could not attach response: ${String(error)}`);
        }

        body = truncatedBody;
      } else if (typeof body === 'object' && body !== null) {
        const bodyStr = JSON.stringify(body, null, 2);
        if (bodyStr.length > 1000) {
          bodyTruncated = true;
          const truncatedBody = bodyStr.substring(0, 500) + '...[truncated]';

          // Attach full response
          try {
            const testInfo = test.info();
            if (testInfo) {
              await testInfo.attach('api-response', {
                body: bodyStr,
                contentType: 'application/json',
              });
              attachmentPath = 'api-response';
            }
          } catch (error) {
            logger.debug(`[API Client] Could not attach response: ${String(error)}`);
          }

          body = truncatedBody;
        }
      }
    } catch (error) {
      body = `[Error reading response body: ${String(error)}]`;
    }

    const logData: ResponseLogData = {
      timestamp,
      url: response.url(),
      status: response.status(),
      statusText: response.statusText(),
      duration,
      headers: response.headers(),
      body,
      bodyTruncated,
      attachmentPath,
      contextId,
    };

    const statusIcon = response.ok() ? '✅' : '❌';
    logger.info(
      `[API Response] ${statusIcon} ${response.status()} ${response.statusText()} (${duration}ms) [Context: ${contextId}]`,
    );

    if (bodyTruncated && attachmentPath) {
      logger.info(`[API Response] Response body truncated. Full response attached: ${attachmentPath}`);
    }

    logger.debug(`[API Response Details] ${JSON.stringify(logData, null, 2)}`);
  }

  // ===============================================
  // Cleanup
  // ===============================================

  /**
   * Dispose of all contexts and cleanup resources
   */
  async dispose(): Promise<void> {
    try {
      await this.contextManager.dispose();
      logger.info('[API Client] Disposed successfully');
    } catch (error) {
      handleException(error, '[API Client] Error during disposal');
    }
  }
}

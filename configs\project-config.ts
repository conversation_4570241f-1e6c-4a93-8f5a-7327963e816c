import { type Project, devices } from '@playwright/test';

// Create reusable launch options
const commonLaunchOptions = {
  args: ['--start-maximized', '--disable-web-security', '--auth-server-allowlist="*"'],
  slowMo: 0,
};

export const uiProjects: Project[] = [
  {
    name: 'chromium',
    testMatch: 'tests/test-management/**/*.spec.ts',
    use: {
      ...devices['Desktop Chrome'],
      viewport: { width: 1920, height: 919 },
      launchOptions: commonLaunchOptions,
    },
  },
];

export const apiProject: Project = {
  name: 'api',
  testMatch: 'tests/test-management/**/*.spec.ts',
  use: {},
};

export const PROJECT_CONFIG: Project[] = [...uiProjects];

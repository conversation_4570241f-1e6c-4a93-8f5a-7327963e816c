import { API_KEY_VALUE, CONTEXT_KEY, createApi<PERSON><PERSON><PERSON><PERSON>, get, getGlobalApiClient, post } from '@api-helper';
import * as process from 'node:process';
import { handleException } from '@report-helper';
import { getBasedData } from '@test-data/handlers/based-data.handler';
import { expect } from '@fixture-manager';
import { APIResponse } from '@playwright/test';

const baseUrl = process.env['API_BASE_URL'] || '';
const baseData = getBasedData();
const endPoint = 'logistics-api/logistics/purchase-orders';

const client = getGlobalApiClient({
  baseUrl,
});

await client.addContext(createApiKeyAuth(CONTEXT_KEY, `${API_KEY_VALUE}`, { headerName: 'x-api-key' }));

/**
 * Add order line to exist purchase order
 * @param purchaseId
 */
export async function addOrderLine(purchaseId: string) {
  try {
    const noStockItem = baseData?.data?.product.find(p => !p.isStockItem);
    // Create user
    const createResponse = await post(`/${endPoint}/${purchaseId}/lines`, {
      contextId: CONTEXT_KEY,
      body: {
        skuId: noStockItem?.skuId || '',
        quantity: 1,
        purchasePrice: 100,
        pricePer: 90,
        purchasePriceInLocalCurrency: 0,
        linePriceRaw: 0,
        linePriceInLocalCurrency: 0,
        exchangeRate: 1,
        linePrice: 100,
        discountRate: 0,
        orderDiscountRate: 0,
        purchaseOrderId: purchaseId,
      },
    });
    expect(createResponse.ok()).toBeTruthy();
  } catch (error) {
    handleException(error, 'Error while adding order line via API');
  }
}

/**
 * Get purchase order by status
 * @param statusId - status id draft = 8
 * @return {Promise<APIResponse>} - API response
 */
export async function getPurchaseOrder(statusId: string): Promise<APIResponse> {
  try {
    const response = await get(`/${endPoint}`, {
      contextId: CONTEXT_KEY,
      queryParams: {
        pageNumber: 1,
        pageSize: 10,
        filter: `statusId="${statusId}"`,
        isTakeAll: false,
      },
    });
    expect(response.ok()).toBeTruthy();
    return response;
  } catch (error) {
    handleException(error, 'Error while retrieving Purchase order');
  }
}

/**
 * Check if purchase order includes lines
 * @param purchaseId - purchase order id
 * @return {Promise<boolean>} - true if purchase order includes lines, false otherwise
 */
export async function isIncludeLines(purchaseId: string): Promise<boolean> {
  try {
    const response = await get(`/${endPoint}/${purchaseId}`, { contextId: CONTEXT_KEY });
    expect(response.ok()).toBeTruthy();
    const responseBody = await response.json();
    return responseBody?.lines?.length > 0;
  } catch (error) {
    handleException(error, 'Error while retrieving Purchase order details');
  }
}

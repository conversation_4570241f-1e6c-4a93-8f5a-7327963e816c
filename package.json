{"name": "bravo-automationed-test", "version": "1.0.0", "private": true, "description": "Strict Playwright + TypeScript test framework with path aliases and custom reporter/expect.", "engines": {"node": ">=22.14.0"}, "scripts": {"format": "prettier --write .", "format:check": "prettier --check .", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts", "test": "playwright test", "test:api": "playwright test --project=api", "test:chromium": "playwright test --project=chromium", "test:firefox": "playwright test --project=firefox", "init-testdata": "node scripts/init-testdata.cjs", "init-testdata-default": "node scripts/init-testdata.cjs --collection based_data --output .test-data/based_data.json", "init-lang-pack-default": "node scripts/init-testdata.cjs --collection language_packs --output .test-data/language_packs.json"}, "devDependencies": {"@playwright/test": "^1.55.0", "@types/node": "^24.3.0", "@typescript-eslint/eslint-plugin": "^8.42.0", "@typescript-eslint/parser": "^8.42.0", "dotenv": "^16.4.5", "eslint": "^9.13.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-playwright": "^1.6.2", "monocart-reporter": "^2.7.7", "prettier": "^3.3.3", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.3", "typescript-eslint": "^8.42.0", "winston": "^3.13.1"}, "dependencies": {"@eslint/js": "^9.34.0", "@types/mongodb": "^4.0.6", "commander": "^14.0.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsdoc": "^54.1.1", "eslint-plugin-prettier": "^5.5.4", "mongodb": "^6.19.0"}, "type": "module"}
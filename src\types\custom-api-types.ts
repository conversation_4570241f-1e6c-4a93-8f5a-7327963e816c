import type { APIRequestContext, APIResponse } from '@playwright/test';

// ===============================================
// Core Types
// ===============================================

export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'HEAD' | 'OPTIONS';

export enum AuthType {
  BEARER = 'bearer',
  BASIC = 'basic',
  API_KEY = 'apiKey',
  OAUTH2 = 'oauth2'
}

// ===============================================
// Authentication Configuration Types
// ===============================================

export interface BaseAuthConfig {
  type: AuthType;
  contextId: string;
  description?: string | undefined | null;
}

export interface BearerAuthConfig extends BaseAuthConfig {
  type: AuthType.BEARER;
  token: string | undefined | null;
  tokenPrefix?: string; // Default: 'Bearer'
}

export interface BasicAuthConfig extends BaseAuthConfig {
  type: AuthType.BASIC;
  username: string;
  password: string;
}

export interface ApiKeyAuthConfig extends BaseAuthConfig {
  type: AuthType.API_KEY;
  apiKey: string | undefined | null;
  headerName?: string; // Default: 'X-API-Key'
  queryParamName?: string | undefined | null; // Alternative to header
}

export interface OAuth2AuthConfig extends BaseAuthConfig {
  type: AuthType.OAUTH2;
  tokenUrl: string;
  clientId: string;
  clientSecret: string;
  scope?: string;
  grantType?: string; // Default: 'client_credentials'
}

export type AuthConfig = 
  | BearerAuthConfig 
  | BasicAuthConfig 
  | ApiKeyAuthConfig 
  | OAuth2AuthConfig;

// ===============================================
// Request and Response Types
// ===============================================

export interface ApiRequestOptions {
  method: HttpMethod;
  url: string;
  headers?: Record<string, string>;
  queryParams?: Record<string, string | number | boolean>;
  body?: unknown;
  contextId?: string; // Specific context to use
  timeout?: number;
}

export interface RequestLogData {
  timestamp: string;
  method: HttpMethod;
  fullUrl: string;
  headers: Record<string, string>;
  queryParams?: Record<string, string | number | boolean> | undefined;
  body?: unknown;
  contextId: string;
}

export interface ResponseLogData {
  timestamp: string;
  url: string;
  status: number;
  statusText: string;
  duration: number;
  headers: Record<string, string>;
  body?: unknown;
  bodyTruncated?: boolean;
  attachmentPath?: string | undefined;
  contextId: string;
}

// ===============================================
// Context Management Types
// ===============================================

export interface AuthContext {
  id: string;
  config: AuthConfig;
  context: APIRequestContext;
  baseUrl: string;
  isActive: boolean;
  createdAt: Date;
  lastUsed?: Date;
  usageCount: number;
}

// ===============================================
// Configuration Types
// ===============================================

export interface ApiClientConfig {
  baseUrl?: string;
  defaultTimeout?: number;
}

// ===============================================
// Utility Types
// ===============================================

export interface ApiError extends Error {
  contextId?: string;
  status?: number;
  statusText?: string;
  response?: APIResponse;
  requestOptions?: ApiRequestOptions;
}

export interface TokenCacheEntry {
  token: string;
  expiresAt: Date;
  tokenType?: string | undefined | null;
}

// ===============================================
// Legacy Compatibility Types
// ===============================================

export interface LegacyApiRequestOptions {
  method: HttpMethod;
  url: string;
  headers?: Record<string, string>;
  query?: Record<string, string | number | boolean>;
  body?: unknown;
  context?: APIRequestContext;
}

export interface LegacyAuthConfig {
  type: 'bearer' | 'basic' | 'apiKey' | 'oauth2';
  token?: string;
  username?: string;
  password?: string;
  apiKey?: string;
  apiKeyHeader?: string;
  tokenUrl?: string;
  clientId?: string;
  clientSecret?: string;
}

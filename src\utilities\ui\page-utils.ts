import { checkNotEmpty, handleException, logger, raiseError } from '@report-helper';
import { SMALL_TIMEOUT } from '@global-timeout';
import { GotoOptions, NavigationOptions, WaitForLoadStateOptions } from '@custom-types';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, TestInfo } from '@playwright/test';
import * as worker_threads from 'node:worker_threads';
import { loadJson } from '@common-helper';
import path from 'node:path';
import process from 'node:process';

// Global page instance used for UI interactions. Ensure this is set before using any utility functions.
let page: Page;
let testInfo: TestInfo;

/**
 * Returns the current Page instance.
 * @returns {Page} The current Page.
 * @throws {Error} If the page has not been set.
 */
export function getPage(): Page {
  if (!page) {
    raiseError('Page instance has not been set.');
  }
  return page;
}

/**
 * Returns the current TestInfo instance.
 * @returns {TestInfo} The current TestInfo.
 * @throws {Error} If TestInfo has not been set.
 */
export function getTestInfo(): TestInfo {
  if (!testInfo) {
    raiseError('TestInfo instance has not been set.');
  }
  return testInfo;
}

/**
 * Sets the current Page instance.
 * @param {Page} pageInstance - The Page instance to set as the current Page.
 */
export function setPage(pageInstance: Page): void {
  page = pageInstance;
}

/**
 * Sets the current TestInfo instance.
 * @param {TestInfo} testInfoInstance - The TestInfo instance to set.
 */
export function setTestInfo(testInfoInstance: TestInfo): void {
  testInfo = testInfoInstance;
}

/**
 * Returns an array of all pages within the current context.
 * @returns {Page[]} An array of Page objects.
 * @throws {Error} If the page has not been set.
 */
export function getAllPages(): Page[] {
  if (!page) {
    raiseError('Page instance has not been set.');
  }
  logger.info('\t\tGetting all pages');
  return page.context().pages();
}

/**
 * Switches to a different page by its 1-based index.
 * If the desired page isn't immediately available, this function will wait and retry for up to 'SMALL_TIMEOUT' milliseconds.
 * @param {number} winNum - The 1-based index of the page to switch to.
 * @throws {Error} If the desired page isn't found within 'SMALL_TIMEOUT' milliseconds or if the page is not set.
 */
export async function switchPage(winNum: number): Promise<void> {
  if (!page) {
    raiseError('Page instance has not been set.');
  }
  const startTime = Date.now();
  while (page.context().pages().length < winNum && Date.now() - startTime < SMALL_TIMEOUT) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  if (page.context().pages().length < winNum) {
    raiseError(`Page number ${winNum} not found after ${SMALL_TIMEOUT} milliseconds`);
  }
  const pageInstance = page.context().pages()[winNum - 1];
  if (!pageInstance) {
    raiseError(`Page at index ${winNum} does not exist.`);
  }
  await pageInstance.waitForLoadState();
  setPage(pageInstance);
}

/**
 * Switches back to the default page (the first one).
 * @throws {Error} If the page has not been set or no pages are available.
 */
export async function switchToDefaultPage(): Promise<void> {
  if (!page) {
    raiseError('Page instance has not been set.');
  }
  const pages = page.context().pages();
  const pageInstance = pages[0];
  if (pageInstance) {
    await pageInstance.bringToFront();
    setPage(pageInstance);
  } else {
    raiseError('No pages available to switch to.');
  }
}

/**
 * Closes a page by its 1-based index.
 * If no index is provided, the current page is closed.
 * If there are other pages open, it will switch back to the default page.
 * @param {number} [winNum] - The 1-based index of the page to close (optional).
 * @throws {Error} If the page has not been set or the index is out of bounds.
 */
export async function closePage(winNum?: number): Promise<void> {
  if (!page) {
    raiseError('Page instance has not been set.');
  }
  const pages = page.context().pages();
  if (winNum === undefined) {
    await page.close();
    return;
  }
  if (winNum < 1 || winNum > pages.length) {
    raiseError(`Page index ${winNum} is out of bounds.`);
  }
  const pageInstance = pages[winNum - 1];
  await pageInstance?.close();
  if (pages.length > 1) {
    await switchToDefaultPage();
  }
}

/**
 * Takes a screenshot and attaches it to the test report.
 * @param {string} [name] - Optional name for the screenshot.
 * @param {Locator} [locator] - Optional locator to screenshot specific element.
 * @param {string} [screenshotPath] - Optional custom path to save screenshot. If not provided, uses default location from config.
 */
export async function takeScreenshot(name?: string, locator?: Locator, screenshotPath?: string): Promise<void> {
  try {
    // Get testInfo without throwing exception if it's null/undefined

    // Determine the final screenshot path from config or use default
    let configPath = './test-results/screenshots'; // Default fallback path

    if (testInfo?.config?.metadata?.['screenshotPath']) {
      configPath = String(testInfo.config.metadata['screenshotPath']);
    } else if (testInfo?.outputDir) {
      configPath = `${testInfo.outputDir}/screenshots`;
    }

    // Use custom path or config path
    const finalPath = screenshotPath || configPath;

    // Import path module to construct full file path
    const path = await import('path');
    const fs = await import('fs');

    // Ensure directory exists
    if (!fs.existsSync(finalPath)) {
      fs.mkdirSync(finalPath, { recursive: true });
    }

    // Create full file path with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = name || `${timestamp}.png`;
    const fullFilePath = path.join(finalPath, fileName);

    // Take screenshot using built-in path option
    if (locator) {
      await locator.screenshot({ path: fullFilePath });
    } else {
      await getPage().screenshot({ path: fullFilePath });
    }

    logger.info(`Screenshot saved to: ${fullFilePath}`);

    // Attach to test report only if testInfo is available
    if (testInfo) {
      try {
        await testInfo.attach(fileName, {
          path: fullFilePath,
          contentType: 'image/png',
        });
      } catch (attachError) {
        logger.info(`Screenshot taken but could not attach to test report: ${String(attachError)}`);
      }
    } else {
      logger.info('Screenshot taken but no test info available for attachment');
    }
  } catch (error) {
    handleException(error, 'Error taking screenshot');
  }
}

/**
 * Navigates to the specified URL.
 * Handles errors related to navigation failures.
 * @param path - The URL to navigate to.
 * @param options - The navigation options. Defaults to `{ waitUntil: LOADSTATE }`.
 * @returns {Promise<null | Response>} - The navigation response or null if no response.
 */
export async function gotoURL(path: string, options: GotoOptions = { waitUntil: 'load' }): Promise<void> {
  try {
    checkNotEmpty(path, 'Path for navigation'); // Ensure path is provided
    logger.info(`Navigating to page URL: ${path}`);
    await getPage().goto(path, options);
  } catch (error) {
    handleException(error, 'Error navigating to URL');
  }
}

/**
 * Waits for a specific page load state.
 * Handles errors related to load state waiting failures.
 * @param options - The navigation options to customize waiting conditions.
 * @returns {Promise<void>} - No return value, it just waits.
 */
export async function waitForPageLoadState(options?: NavigationOptions): Promise<void> {
  try {
    let waitUntil: WaitForLoadStateOptions = 'load'; // Default load state

    if (options?.waitUntil && options.waitUntil !== 'commit') {
      waitUntil = options.waitUntil; // Override default if specified in options
    }

    await getPage().waitForLoadState(waitUntil);
  } catch (error) {
    handleException(error, 'Error waiting for page load state');
  }
}

/**
 * Reloads the current page.
 * Waits for the page to be reloaded and waits for a load state.
 * @param options - The navigation options to customize the reload.
 * @returns {Promise<void>} - No return value, it just reloads the page.
 */
export async function reloadPage(options?: NavigationOptions): Promise<void> {
  try {
    await Promise.all([getPage().reload(options), getPage().waitForEvent('framenavigated')]);
    logger.info(`Reloaded the current page`);
    await waitForPageLoadState(options); // Wait for the page to load after reload
  } catch (error) {
    handleException(error, 'Error reloading the page');
  }
}

/**
 * Navigates back to the previous page.
 * Waits for the page to navigate back and waits for a load state.
 * @param options - The navigation options to customize the goBack action.
 * @returns {Promise<void>} - No return value, it just navigates back.
 */
export async function goBack(options?: NavigationOptions): Promise<void> {
  try {
    await Promise.all([getPage().goBack(options), getPage().waitForEvent('framenavigated')]);
    logger.info(`Navigated back to the previous page`);
    await waitForPageLoadState(options); // Wait for the page to load after navigating back
  } catch (error) {
    handleException(error, 'Error navigating back');
  }
}

/**
 * Saves the storage state of the page.
 * @param {string} [path] - Optional path to save the storage state to.
 * @returns {Promise<void>} - No return value, just saves the storage state.
 */
export async function saveStorageState(path?: string): Promise<void> {
  try {
    if (path !== undefined) {
      await getPage().context().storageState({ path });
      logger.info(`Stored current page context to ${path}`);
    } else {
      await getPage().context().storageState();
      logger.info(`Stored current page context (no path specified)`);
    }
  } catch (error) {
    handleException(error, 'Error saving storage state');
  }
}

/**
 * Saves the storage state of the page.
 * @param name - the cookies name to save
 * @returns {Promise<string|undefined>} - No return value, just saves the storage state.
 */
export async function getCookie(name: string): Promise<string | undefined> {
  try {
    let cookies: Cookie[];
    if (page)
      cookies = await getPage().context().cookies();
    else {
      // base on current test worker id, get the existing
      const workerId = testInfo.parallelIndex ?? worker_threads.threadId;
      const finalPath: string = path.resolve(process.env['AUTH'] || '.auth', process.env['NODE_ENV'] || '', `${workerId}.json`);
      const data = loadJson<any>(finalPath || '');
      const cookiesNode = data?.cookies; // Find the Cookies node
      cookies = Array.isArray(cookiesNode) ? cookiesNode : []; // Ensure it's an array

    }
    return cookies.find(cookie => cookie.name.toLowerCase() === name.toLowerCase())?.value;
  } catch (error) {
    handleException(error, 'Error saving cookie');
  }
}

/**
 * Returns the URL of the page.
 * @param {NavigationOptions} [options] - Optional navigation options.
 * @returns {Promise<string>} - The URL of the page.
 */
export async function getURL(options: NavigationOptions = { waitUntil: 'load' }): Promise<string> {
  try {
    await waitForPageLoadState(options);
    let currentUrl = getPage().url();
    logger.info(`Current URL of page is: ${currentUrl}`);
    return currentUrl;
  } catch (error) {
    // Using your error handler function for consistent error logging
    handleException(error, 'Error retrieving URL');
  }
}

/**
 * Clears all cookies in the current browser context.
 * Also clears sessionStorage and localStorage in the current page.
 * @returns {Promise<void>} - No return value, just clears cookies and storage.
 */
export async function clearCookies(): Promise<void> {
  try {
    await getPage().context().clearCookies();
    await getPage().evaluate(() => {
      try {
        sessionStorage.clear();
      } catch {
        /* Ignore sessionStorage clear errors */
      }
      try {
        localStorage.clear();
      } catch {
        /* Ignore localStorage clear errors */
      }
    });
    logger.info('Cleared all cookies and storage in the current context');
  } catch (error) {
    handleException(error, 'Error clearing cookies, sessionStorage, and localStorage');
  }
}

/**
 * Waits until the page URL contains the expected URL string.
 * @param expectedUrl - The URL string that should be contained in the current URL.
 * @param options - Optional settings: timeout (ms), waitUntil load state (default 'load').
 * @returns The final matched URL.
 */
export async function waitForUrl(
  expectedUrl: string,
  options: { timeout?: number; waitUntil?: WaitForLoadStateOptions } = {},
): Promise<string> {
  try {
    checkNotEmpty(expectedUrl, 'Expected URL');

    const { timeout = 30000, waitUntil = 'load' } = options;
    const pageInstance = getPage();

    logger.info(`Waiting for URL to contain: ${expectedUrl}, timeout=${timeout}, waitUntil=${waitUntil}`);

    await pageInstance.waitForLoadState(waitUntil);

    await pageInstance.waitForFunction((needle: string) => window.location.href.includes(needle), expectedUrl, {
      timeout,
    });

    const finalUrl = pageInstance.url();
    logger.info(`URL matched (contains): ${finalUrl}`);
    return finalUrl;
  } catch (error) {
    handleException(error, 'Error waiting for URL');
  }
}

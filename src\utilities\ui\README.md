# UI Utilities

Framework-level helpers layered above <PERSON><PERSON> for consistency and logging.

## Key Files

- `page-utils.ts`: Page lifecycle (set/get page), navigation helpers, screenshots, waits.
- `ui-actions.ts` / `ui-actions.ts`: Higher-level wrapper functions (click, fill, etc.) applying timeouts & logging.
- `locator-helper.ts` / `parameter-types.ts`: Selector builders and typed parameter patterns.
- `assert-utils.ts`: Common assertion shortcuts.
- `ui-actions.ts`: Composite user flows (if present) reusing primitive actions.

## Principles

1. Tests never call `page.locator` directly—only page objects or these actions.
2. Centralize timeouts & logging; mask sensitive input values.
3. Provide resilience (auto-retry patterns kept minimal & explicit).

## Example

```typescript
import { click, fill } from '@ui-action';
await fill('input[name="email"]', '<EMAIL>');
await click('button:has-text("Submit")');
```

## Adding New Actions

1. Implement the action in `ui-actions.ts`:

   ```typescript
   export async function customClick(selector: string): Promise<void> {
     const page = await getPage();
     await page.click(selector);
     logger.info(`Clicked on ${selector}`);
   }
   ```

2. Export the action in the folder's `index.ts`:

   ```typescript
   export * from './ui-actions';
   ```

3. Use the action in your tests:

   ```typescript
   import { customClick } from '@ui-action';
   await customClick('button:has-text("Save")');
   ```

import { test, expect } from '@playwright/test';
import { ApiClient, createBearerAuth, createBasicAuth, createApiKeyAuth } from '@api-helper';

test.describe('Simplified Multi-Context API Client', () => {
  let client: ApiClient;

  test.beforeEach( () => {
    client = new ApiClient({
      baseUrl: 'https://jsonplaceholder.typicode.com'
    });
  });

  test.afterEach(async () => {
    if (client) {
      await client.dispose();
    }
  });

  test('should create authentication contexts', () => {
    // Test Bearer auth
    const bearerAuth = createBearerAuth('user1', 'test-token');
    expect(bearerAuth.type).toBe('bearer');
    expect(bearerAuth.contextId).toBe('user1');
    expect(bearerAuth.token).toBe('test-token');

    // Test Basic auth
    const basicAuth = createBasicAuth('user2', 'username', 'password');
    expect(basicAuth.type).toBe('basic');
    expect(basicAuth.contextId).toBe('user2');
    expect(basicAuth.username).toBe('username');
    expect(basicAuth.password).toBe('password');

    // Test API Key auth
    const apiKeyAuth = createApiKeyAuth('service1', 'api-key-123', {
      headerName: 'X-API-Key'
    });
    expect(apiKeyAuth.type).toBe('apiKey');
    expect(apiKeyAuth.contextId).toBe('service1');
    expect(apiKeyAuth.apiKey).toBe('api-key-123');
    expect(apiKeyAuth.headerName).toBe('X-API-Key');
  });

  test('should add and manage contexts', async () => {
    const bearerAuth = createBearerAuth('user1', 'test-token');

    // Add context
    const contextId = await client.addContext(bearerAuth);
    expect(contextId).toBe('user1');

    // Check contexts
    const contexts = client.getContexts();
    expect(contexts).toContain('user1');

    const activeContexts = client.getActiveContexts();
    expect(activeContexts).toContain('user1');

    // Remove context
    await client.removeContext('user1');
    const contextsAfterRemoval = client.getContexts();
    expect(contextsAfterRemoval).not.toContain('user1');
  });

  test('should handle multiple contexts with rotation', async () => {
    // Add multiple contexts
    await client.addContext(createBearerAuth('user1', 'token1'));
    await client.addContext(createBearerAuth('user2', 'token2'));
    await client.addContext(createBearerAuth('user3', 'token3'));

    // Check all contexts are available
    const contexts = client.getActiveContexts();
    expect(contexts).toHaveLength(3);
    expect(contexts).toContain('user1');
    expect(contexts).toContain('user2');
    expect(contexts).toContain('user3');
  });

  test('should throw error when no contexts available', async () => {
    // Try to make request without any contexts
    await expect(client.get('/posts/1')).rejects.toThrow('No active contexts available');
  });

  test('should throw error when specified context not found', async () => {
    await client.addContext(createBearerAuth('user1', 'test-token'));

    // Try to use non-existent context
    await expect(
      client.get('/posts/1', { contextId: 'non-existent' })
    ).rejects.toThrow("Context 'non-existent' not found or inactive");
  });
});

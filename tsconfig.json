// tsconfig.json
{
  "compilerOptions": {
    /* === Output & Target Environment === */
    "target": "ES2022",
    // Downlevel target for emitted JS features (syntax baseline)
    "module": "ESNext",
    // Preserve modern ES modules (lets bundler optimize)
    "lib": ["ES2022", "DOM"],
    // Type defs available globally (ES + browser APIs)
    "noEmit": false,
    // Do not emit JS (handled by bundler / tsc --noEmit false for build)
    "outDir": "dist",
    // Output directory for compiled JS (tsc will emit here)

    /* === Module Resolution & Bundler Integration === */
    "moduleResolution": "Bundler",
    // Modern resolution aligning with tooling (e.g. Vite / bundlers)
    "resolveJsonModule": true,
    // Allow `import x from './file.json'`
    "esModuleInterop": true,
    // Enables default import compatibility for CommonJS
    "types": ["node", "@playwright/test"],
    // Inject Node + Playwright ambient types

    /* === Strictness & Type Safety === */
    "strict": true,
    // Enables all strict mode flags
    "noUncheckedIndexedAccess": true,
    // Adds `undefined` to out-of-bounds indexed access
    "noImplicitOverride": true,
    // Forces explicit `override` keyword
    "noPropertyAccessFromIndexSignature": true,
    // Disallow property-style access when only index signature exists
    "exactOptionalPropertyTypes": true,
    // `?` properties are not implicitly widened
    "forceConsistentCasingInFileNames": true,
    // Enforce consistent import casing
    "skipLibCheck": true,
    // Skip type checking of .d.ts (speeds build)

    /* === Language Surface Control === */
    "allowJs": false,
    // Disallow compiling .js (TS only)

    /* === Base Path & Path Mapping (Import Aliases) === */
    "baseUrl": ".",
    // Root for resolving non-relative modules
    "paths": {
      "@custom-types": ["src/types"],
      "@common-helper": ["src/utilities/common"],
      "@database-helper": ["src/utilities/database"],
      "@locator-helper": ["src/utilities/ui/locator-helper.ts"],
      "@api-helper": ["src/utilities/api"],
      "@ui-action": ["src/utilities/ui"],
      "@page-utils": ["src/utilities/ui/page-utils"],
      "@report-helper": ["src/utilities/reporter"],
      "@assert-helper": ["src/utilities/reporter/custom-expect"],
      "@global-config": ["configs"],
      "@global-timeout": ["configs/timeout.config.ts"],
      "@fixture-manager": ["tests/test-objects/fixture-manager.ts"],
      "@components/*": ["tests/test-objects/gui/components/*"],
      "@page-objects/*": ["tests/test-objects/gui/page-objects/*"],
      "@api-objects/*": ["tests/test-objects/api/*"],
      "@test-data/*": ["tests/test-data/*", "tests/test-management/test-preparation/*", "tests/data-management/*"]
    }
  },
  /* === File Inclusion / Exclusion === */
  "include": [
    "playwright.config.ts",
    "configs/**/*.ts",
    "src/**/*.ts",
    "tests/**/*.ts",
    "src/types/**/*.d.ts",
    "scripts/**/*.ts"
  ],
  "exclude": [
    ".idea",
    ".vs",
    ".vscode",
    "test-results",
    "node_modules",
    "dist"
    // Prevent already-built output from being recompiled
  ]
}

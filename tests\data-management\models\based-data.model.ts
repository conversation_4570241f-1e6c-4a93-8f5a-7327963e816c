export interface Kunde {
  id: string;
  name: string;
}

export interface CompanyContact {
  name: string;
}

export interface Company {
  id: string;
  name: string;
  emailToSendInvoice: string;
  erpNumber: string;
  contact: CompanyContact;
}

export interface Product {
  id: string;
  productNo: string;
  name: string;
  description: string;
  skuId: string;
  skuName: string;
  skuNumber: string;
  hasAttachments: boolean;
  hasSubscription: boolean;
  isStockItem: boolean;
  bundleItems: string[];
}

export interface Department {
  id: string;
  name: string;
}

export interface Person {
  id: string;
  name: string;
  usingFor: string;
}

export interface BaseData {
  kunde: Kunde;
  company: Company;
  supplier: string[];
  product: Product[];
  department: Department[];
  person: Person[];
}

export interface BasedDataDocument {
  _id: string;
  env: string;
  data: BaseData;
}
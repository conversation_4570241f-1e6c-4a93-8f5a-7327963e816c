import { takeScreenshot } from '@page-utils';
import { test } from '@fixture-manager';
import { expectElementToBeVisible } from '@ui-action';
import { MAX_TIMEOUT } from '@global-timeout';
import { MenuPaths, navigateToMenu } from '@components/navigation.func';
import { getPurchaseOrder, isIncludeLines } from '@api-objects/helpers/purchase-order.api';

test.describe('Purchase Order', () => {
  test('Send Purchase Order to Approve flow', async ({purchaseOrderPage}) => {
     await navigateToMenu(MenuPaths.CRM['Purchase/Sales Order'].PurchaseOrders);
    const response = await getPurchaseOrder('8');
    const purchaseOrders: any = await response?.json();
    for (const purchaseOrder of purchaseOrders.items) {
      let id: string = purchaseOrder.id;
      if (await isIncludeLines(id)) {
        await purchaseOrderPage.openPurchaseOrderDetails(id);
        break;
      }
    }
    // Test implementation
    await expectElementToBeVisible('img[alt=Bravo]', { timeout: MAX_TIMEOUT });
    // Add your test logic here
    await takeScreenshot();
  });
});

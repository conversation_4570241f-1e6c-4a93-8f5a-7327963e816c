import { request, type APIRequestContext, type APIResponse } from '@playwright/test';
import { handleException, logger, raiseError } from '@report-helper';
import { ContentTypes } from '@api-helper';

// ===============================================
// Types and Interfaces
// ===============================================
export type ApiMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

export interface ApiRequestOptions {
  method: ApiMethod;
  url: string; // Can be endpoint ("/users") or full URL
  headers?: Record<string, string>;
  query?: Record<string, string | number | boolean>;
  body?: unknown;
  context?: APIRequestContext; // Optional context override
}

export interface TokenResponse {
  access_token: string;
  token_type?: string;
  expires_in?: number;
}

export interface AuthConfig {
  type: 'bearer' | 'basic' | 'apiKey' | 'oauth2';
  token?: string;
  username?: string;
  password?: string;
  apiKey?: string;
  apiKeyHeader?: string;
  tokenUrl?: string;
  clientId?: string;
  clientSecret?: string;
}

// ===============================================
// API Helper Class with Internal Context Management
// ===============================================

export class ApiHelper {
  private static defaultContext: APIRequestContext | null = null;
  private static authConfig: AuthConfig | null = null;
  private static baseURL: string | null = null;

  /**
   * Initialize the API Helper with configuration
   */
  static async initialize(baseURL?: string, authConfig?: AuthConfig): Promise<void> {
    try {
      this.baseURL = baseURL || this.getBaseUrlFromEnv();
      this.authConfig = authConfig || null;

      logger.info(`[API Helper] Initializing with base URL: ${this.baseURL}`);

      // Create default context
      this.defaultContext = await request.newContext({ baseURL: this.baseURL });

      // Setup authentication if provided
      if (this.authConfig) {
        await this.setupAuthentication();
      }

      logger.info(`[API Helper] Successfully initialized`);
    } catch (error) {
      handleException(error, `[API Helper] Failed to initialize`);
    }
  }

  /**
   * Get base URL from environment variables
   */
  private static getBaseUrlFromEnv(): string {
    const url = process.env['API_BASE_URL'] || process.env['BASE_URL'];
    if (!url) {
      handleException(
        new Error('No base URL found in environment variables'),
        `[API Helper] No base URL found in environment variables`,
      );
    }
    return url;
  }

  /**
   * Setup authentication based on auth config
   */
  private static async setupAuthentication(): Promise<void> {
    try {
      if (!this.authConfig || !this.defaultContext) return;

      logger.info(`[API Helper] Setting up ${this.authConfig.type} authentication`);

      switch (this.authConfig.type) {
        case 'oauth2':
          if (this.authConfig.tokenUrl && this.authConfig.clientId && this.authConfig.clientSecret) {
            this.authConfig.token = await this.getBearerToken(
              this.authConfig.tokenUrl,
              this.authConfig.clientId,
              this.authConfig.clientSecret,
            );
            logger.info(`[API Helper] OAuth2 token acquired successfully`);
          }
          break;
        case 'bearer':
          logger.info(`[API Helper] Bearer token authentication configured`);
          break;
        case 'basic':
          logger.info(`[API Helper] Basic authentication configured`);
          break;
        case 'apiKey':
          logger.info(`[API Helper] API Key authentication configured`);
          break;
      }
    } catch (error) {
      handleException(error, `[API Helper] Authentication setup failed`);
    }
  }

  /**
   * Get authentication headers based on current auth config
   */
  private static getAuthHeaders(): Record<string, string> {
    if (!this.authConfig) return {};

    try {
      switch (this.authConfig.type) {
        case 'bearer':
        case 'oauth2':
          return this.authConfig.token ? { Authorization: `Bearer ${this.authConfig.token}` } : {};

        case 'basic':
          if (this.authConfig.username && this.authConfig.password) {
            const credentials = Buffer.from(`${this.authConfig.username}:${this.authConfig.password}`).toString(
              'base64',
            );
            return { Authorization: `Basic ${credentials}` };
          }
          return {};

        case 'apiKey': {
          const header = this.authConfig.apiKeyHeader || 'X-API-Key';
          return this.authConfig.apiKey ? { [header]: this.authConfig.apiKey } : {};
        }

        default:
          return {};
      }
    } catch {
      logger.error(`[API Helper] Failed to generate auth headers`);
      return {};
    }
  }

  /**
   * Get bearer token using OAuth2 client credentials flow
   */
  private static async getBearerToken(tokenUrl: string, clientId: string, clientSecret: string): Promise<string> {
    if (!this.defaultContext) {
      raiseError('API Helper not initialized. Call ApiHelper.initialize() first.');
    }

    try {
      logger.info(`[API Helper] Fetching OAuth2 token from ${tokenUrl}`);

      const response = await this.defaultContext.fetch(tokenUrl, {
        method: 'POST',
        headers: { 'content-type': ContentTypes.form },
        data: new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: clientId,
          client_secret: clientSecret,
        }).toString(),
      });

      if (!response.ok()) {
        const errorText = await response.text();
        logger.error(`[API Helper] Token fetch failed: ${response.status()} ${response.statusText()} - ${errorText}`);
        raiseError(`Token fetch failed: ${response.status()} - ${errorText}`);
      }

      const json = (await response.json()) as TokenResponse;
      logger.info(`[API Helper] OAuth2 token received successfully`);
      return json.access_token;
    } catch (error) {
      handleException(error, `[API Helper] OAuth2 token acquisition failed`);
    }
  }

  /**
   * Get the context to use for requests (custom, default, or auto-created)
   */
  private static async getContext(customContext?: APIRequestContext): Promise<APIRequestContext> {
    if (customContext) return customContext;

    if (this.defaultContext) return this.defaultContext;

    // Auto-initialize if not already done
    logger.info(`[API Helper] Auto-initializing with environment base URL`);
    await this.initialize();

    if (!this.defaultContext) {
      raiseError('Failed to auto-initialize API Helper');
    }

    return this.defaultContext;
  }

  /**
   * Main API request method with automatic context and auth handling
   */
  static async request(options: ApiRequestOptions): Promise<APIResponse> {
    try {
      const { method, url, headers = {}, query, body, context } = options;
      const startTime = Date.now();
      logger.info(`[API Request] Starting ${method} request to: ${url}`);

      // Get context (custom, default, or auto-created)
      const ctx = await this.getContext(context);

      // Build full URL
      let fullUrl = url;
      if (url.startsWith('/') && this.baseURL) {
        fullUrl = this.baseURL.replace(/\/$/, '') + url;
      }
      fullUrl = withQuery(fullUrl, query);

      // Merge auth headers with request headers
      const authHeaders = this.getAuthHeaders();
      const finalHeaders = { ...authHeaders, ...headers };

      // Compose request options
      let reqOpts: Parameters<APIRequestContext['fetch']>[1] = { method };
      reqOpts = withHeader(reqOpts, finalHeaders);
      reqOpts = withBody(reqOpts, body);

      // Log request with proper formatting
      logRequest(reqOpts, fullUrl);

      const res = await ctx.fetch(fullUrl, reqOpts);

      // Log response details in pretty format
      let responseBody: unknown = undefined;
      try {
        responseBody = await res.json();
      } catch {
        try {
          responseBody = await res.text();
        } catch {
          // ignore
        }
      }

      // Log response with proper formatting
      logResponse(res, responseBody, startTime);

      if (!res.ok()) {
        logger.error(`[API Request] Request failed with status ${res.status()}: ${res.statusText()}`);
      } else {
        logger.info(`[API Request] Request completed successfully`);
      }

      return res;
    } catch (error) {
      handleException(error, `[API Request] Request failed`);
    }
  }

  /**
   * Update authentication configuration
   */
  static async updateAuth(authConfig: AuthConfig): Promise<void> {
    try {
      logger.info(`[API Helper] Updating authentication to type: ${authConfig.type}`);
      this.authConfig = authConfig;
      await this.setupAuthentication();
      logger.info(`[API Helper] Authentication updated successfully`);
    } catch (error) {
      handleException(error, `[API Helper] Failed to update authentication`);
    }
  }

  /**
   * Create a custom context for specific use cases
   */
  static async createCustomContext(baseURL?: string): Promise<APIRequestContext> {
    try {
      const url = baseURL || this.baseURL || this.getBaseUrlFromEnv();
      logger.info(`[API Helper] Creating custom API context with base URL: ${url}`);
      return await request.newContext({ baseURL: url });
    } catch (error) {
      handleException(error, `[API Helper] Failed to create custom context`);
    }
  }

  /**
   * Cleanup - dispose of default context
   */
  static async dispose(): Promise<void> {
    try {
      if (this.defaultContext) {
        await this.defaultContext.dispose();
        this.defaultContext = null;
        logger.info('[API Helper] Disposed successfully');
      }
    } catch (error) {
      logger.error(`[API Helper] Error during disposal: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // ===============================================
  // Convenience Methods
  // ===============================================

  static async get(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
    return this.request({ method: 'GET', url, ...options });
  }

  static async post(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
    return this.request({ method: 'POST', url, ...options });
  }

  static async put(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
    return this.request({ method: 'PUT', url, ...options });
  }

  static async patch(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
    return this.request({ method: 'PATCH', url, ...options });
  }

  static async del(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
    return this.request({ method: 'DELETE', url, ...options });
  }
}

// ===============================================
// Utility Functions
// ===============================================

export function getApiKey(keyName?: string | null): string {
  const key = process.env[keyName || ''];
  if (!key) {
    raiseError(`API key not found in environment variable: ${keyName}`);
  }
  return key;
}

export function withQuery(url: string, query?: Record<string, string | number | boolean>): string {
  if (!query) return url;
  const usp = new URLSearchParams();
  Object.entries(query).forEach(([k, v]) => usp.set(k, String(v)));
  const join = url.includes('?') ? '&' : '?';
  return `${url}${join}${usp.toString()}`;
}

export function withBody<T extends object>(opts: T, body?: unknown): T & { data?: unknown } {
  return body !== undefined ? { ...opts, data: body } : opts;
}

export function withHeader<T extends object>(
  opts: T,
  headers?: Record<string, string>,
): T & { headers?: Record<string, string> } {
  if (!headers) return opts;

  const existingHeaders = (opts as { headers?: Record<string, string> }).headers || {};
  return { ...opts, headers: { ...existingHeaders, ...headers } };
}

// ===============================================
// Standalone Functions (Auto-Initialize if Needed)
// ===============================================

/**
 * Standalone GET request - auto-initializes if needed
 */
export async function get(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
  try {
    return await ApiHelper.get(url, options);
  } catch (error) {
    handleException(error, `[Standalone GET] Request failed`);
  }
}

/**
 * Standalone POST request - auto-initializes if needed
 */
export async function post(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
  try {
    return await ApiHelper.post(url, options);
  } catch (error) {
    handleException(error, `[Standalone POST] Request failed`);
  }
}

/**
 * Standalone PUT request - auto-initializes if needed
 */
export async function put(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
  try {
    return await ApiHelper.put(url, options);
  } catch (error) {
    handleException(error, `[Standalone PUT] Request failed`);
  }
}

/**
 * Standalone PATCH request - auto-initializes if needed
 */
export async function patch(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
  try {
    return await ApiHelper.patch(url, options);
  } catch (error) {
    handleException(error, `[Standalone PATCH] Request failed`);
  }
}

/**
 * Standalone DELETE request - auto-initializes if needed
 */
export async function del(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
  try {
    return await ApiHelper.del(url, options);
  } catch (error) {
    handleException(error, `[Standalone DELETE] Request failed`);
  }
}

// In your api-helper.ts
function logRequest(request: Parameters<APIRequestContext['fetch']>[1], url?: string) {
  const formattedLog = [
    '',
    '=== REQUEST ===',
    `URL: ${url || (request as { url?: string }).url || 'N/A'}`,
    `Method: ${(request as { method?: string }).method || 'GET'}`,
    `Headers: ${JSON.stringify((request as { headers?: Record<string, string> }).headers || {}, null, 2)}`,
    `Body: ${
      (request as { body?: unknown }).body
        ? JSON.stringify(
            (
              request as {
                body?: unknown;
              }
            ).body,
            null,
            2,
          )
        : '{}'
    }`,
    '==================',
  ].join('\n');

  logger.info(formattedLog);
}

function logResponse(response: APIResponse, responseBody?: unknown, startTime?: number) {
  const endTime = Date.now();
  const duration = startTime ? `${endTime - startTime}ms` : 'N/A';

  const formattedLog = [
    '',
    '=== RESPONSE ===',
    `URL: ${response.url()}`,
    `Status Code: ${response.status()}`,
    `Status Text: ${response.statusText()}`,
    `Success: ${response.ok()}`,
    `Response Time: ${duration}`,
    `Headers: ${JSON.stringify(response.headers(), null, 2)}`,
    `Body: ${responseBody ? JSON.stringify(responseBody, null, 2) : 'No body'}`,
    '===================',
  ].join('\n');
  logger.info(formattedLog);
}

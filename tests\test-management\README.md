# tests/test-management/

Purpose: Organize test specifications by type (GUI/API) and preparation steps; demonstrate role-based auth + data-driven patterns.

- gui/: UI specs that use Page Objects and @page-action wrappers
- api/: API specs that use createApiContext and apiRequest helpers
- test-preparation/: docs and scripts for setup/teardown of external data

Patterns:

- Positive and negative coverage
- Data-driven examples (see `test-data/users.json`)
- Role-based auth via storage state fixtures (`signInAsAdmin`, `signInAsOther`)
- API and GUI kept separate; GUI uses page objects only

Quick Auth Usage:

```ts
import { test } from '@fixture-manager';
test.use({ storageState: ({ signInAsAdmin }) => signInAsAdmin });
test('admin dashboard', async ({ page }) => {
  /* already signed in */
});
```

Relationships:

- Relies on utilities under src/utilities and page objects under tests/test-objects

import {
  clearCookies,
  gotoURL,
  saveStorageState,
  setPage,
  closePage,
  getPage,
  waitForUrl,
  waitForPageLoadState,
  getTestInfo,
} from '@page-utils';
import { logger, handleException } from '@report-helper';
import { click, fill, fillAndEnter, isElementVisible } from '@ui-action';
import { expectElementToBeVisible } from 'src/utilities/ui/assert-utils';
import { MAX_TIMEOUT } from '@global-timeout';
import path from 'path';
import fs from 'fs';
import { getLocatorByRole, getLocatorByText } from '@locator-helper';
import type { Browser, BrowserContext, Page } from '@playwright/test';
import { test as baseTest } from '@global-config';
import * as process from 'node:process';

// Locators
const signInBtn = () => getLocatorByRole('button', { name: 'Sign in' });
const isStaySignedInLbl = () => getLocatorByText('Stay signed in?');
const yesBtn = () => getLocatorByRole('button', { name: 'Yes' });

// Constants
const DEFAULT_AUTH_DIR = process.env['AUTH_PATH'] || '.auth';

/**
 * Gets the current worker ID from test info or falls back to 0
 * @returns number - The current worker ID
 */
function getCurrentWorkerId(): number {
  try {
    // Try to get from current test info
    const testInfo = getTestInfo();
    return testInfo.parallelIndex;
  } catch {
    try {
      // Fallback to baseTest info
      return baseTest.info().parallelIndex;
    } catch {
      // Final fallback
      logger.warn('[getCurrentWorkerId] Could not get worker ID from test info, using 0');
      return 0;
    }
  }
}

/**
 * Configuration interface for authentication context
 */
interface AuthContextConfig {
  baseUrl?: string;
  extraHeaders?: Record<string, string>;
}

/**
 * Interface for storage state options
 */
interface StorageStateOptions {
  storagePath?: string;
  workerId?: number;
  reuseExisting?: boolean;
}

/**
 * Creates a browser context with proper authentication headers for Microsoft login.
 * This prevents unwanted redirects and ensures proper token handling.
 * @param browser - The browser instance
 * @param config - Optional configuration for auth context
 * @returns Promise<BrowserContext> - The created context with auth headers
 */
export async function createAuthContext(browser: Browser, config: AuthContextConfig = {}): Promise<BrowserContext> {
  const baseUrl = config.baseUrl || process.env['BASE_URL'];

  if (!baseUrl) {
    throw new Error('BASE_URL environment variable is not set and no baseUrl provided in config');
  }

  const defaultHeaders = {
    Origin: baseUrl,
    Referer: baseUrl,
  };

  const extraHTTPHeaders = { ...defaultHeaders, ...config.extraHeaders };

  logger.info(`[createAuthContext] Creating context with headers for: ${baseUrl}`);

  return await browser.newContext({ extraHTTPHeaders });
}

/**
 * Creates a new page within an authentication context and sets it as the current page.
 * @param browser - The browser instance
 * @param config - Optional configuration for auth context
 * @returns Promise<Page> - The created page with auth context
 */
export async function createAuthPage(browser: Browser, config: AuthContextConfig = {}): Promise<Page> {
  const context = await createAuthContext(browser, config);
  const page = await context.newPage();
  setPage(page);
  logger.info('[createAuthPage] Created new page with auth context');
  return page;
}

/**
 * Log in to the application using the current page.
 * @param username - User's login username
 * @param password - User's login password
 */
export async function signIn(username: string, password: string): Promise<void> {
  logger.info(`[signIn] Starting login process for user: ${username}`);

  await clearCookies();
  await gotoURL(`${process.env['BASE_URL']}/uptime`);

  await fillAndEnter('input[name="loginfmt"]', username);
  await fill('input[name="passwd"]', password);
  await click(signInBtn());

  logger.info('[signIn] Login form submitted');
  const staySignedIn = await isElementVisible(isStaySignedInLbl());

  if (staySignedIn) {
    await click(yesBtn());
    logger.info('[handleStaySignIn] Clicked "Yes" on stay signed in prompt');
  } else {
    logger.info('[handleStaySignIn] No stay signed in prompt found');
  }
}

/**
 * Complete the post-login verification process.
 * Waits for successful navigation and verifies the application loaded correctly.
 */
export async function verifyLoginSuccess(): Promise<void> {
  logger.info('[verifyLoginSuccess] Verifying login success');

  await waitForUrl(process.env['BASE_URL'] ?? 'bravo');
  await waitForPageLoadState();
  await expectElementToBeVisible('img[alt=Bravo]', { timeout: MAX_TIMEOUT });

  logger.info('[verifyLoginSuccess] Login verification completed successfully');
}

/**
 * Validates if a storage state file exists and contains valid data.
 * @param filePath - Path to the storage state file
 * @returns boolean - True if file is valid, false otherwise
 */
function isValidStorageState(filePath: string): boolean {
  try {
    if (!fs.existsSync(filePath)) {
      logger.debug(`[isValidStorageState] File does not exist: ${filePath}`);
      return false;
    }

    const stat = fs.statSync(filePath);
    if (stat.size === 0) {
      logger.debug(`[isValidStorageState] File is empty: ${filePath}`);
      return false;
    }

    // Validate JSON structure
    const content = fs.readFileSync(filePath, 'utf-8');
    JSON.parse(content);

    logger.debug(`[isValidStorageState] Valid storage state found: ${filePath}`);
    return true;
  } catch (error) {
    logger.warn(`[isValidStorageState] Invalid storage state file: ${filePath}`, { value: error });
    return false;
  }
}

/**
 * Resolves the final storage path based on provided options.
 * @param options - Storage state options
 * @returns string - The resolved file path
 */
function resolveStoragePath(options: StorageStateOptions = {}): string {
  const { storagePath, workerId } = options;

  // Use provided workerId or get current worker ID dynamically
  const effectiveWorkerId = workerId ?? getCurrentWorkerId();

  // Case 1: No storage path provided, use default
  if (!storagePath) {
    return path.resolve(process.cwd(), DEFAULT_AUTH_DIR, `${effectiveWorkerId}.json`);
  }

  // Case 2: Explicit file path (ends with .json)
  if (storagePath.endsWith('.json')) {
    return path.resolve(storagePath);
  }

  // Case 3: Directory path - append worker ID file
  try {
    const isDirectory = fs.existsSync(storagePath) && fs.statSync(storagePath).isDirectory();
    if (isDirectory) {
      return path.join(storagePath, `${effectiveWorkerId}.json`);
    }
  } catch {
    // Path doesn't exist, treat as directory
  }

  // Default: treat as directory and append worker file
  return path.join(storagePath, `${effectiveWorkerId}.json`);
}

/**
 * Ensures the directory for a file path exists.
 * @param filePath - The file path whose directory should be created
 */
function ensureDirectoryExists(filePath: string): void {
  const directory = path.dirname(filePath);
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory, { recursive: true });
    logger.info(`[ensureDirectoryExists] Created directory: ${directory}`);
  }
}

/**
 * Auto login with storage state management.
 * Checks if valid storage exists, if yes returns path, if no creates new storage.
 * @param browser - Browser instance
 * @param username - Username for login
 * @param password - Password for login
 * @param options - Optional storage path and worker ID
 * @returns Promise<string> - Path to the storage state file
 */
export async function autoLogin(
  browser: Browser,
  username: string,
  password: string,
  options: StorageStateOptions = {},
): Promise<string> {
  const finalPath: string = resolveStoragePath(options);
  const workerId = options.workerId ?? getCurrentWorkerId();

  logger.info(`[autoLogin] Worker ID: ${workerId}, Checking storage state for user: ${username}, path: ${finalPath}`);

  // Check if valid storage exists
  if (isValidStorageState(finalPath)) {
    logger.info(`[autoLogin] Valid storage state found, reusing: ${finalPath}`);
    return finalPath;
  }

  logger.info(`[autoLogin] No valid storage state found, creating new one: ${finalPath}`);

  // Ensure directory exists
  ensureDirectoryExists(finalPath);

  // Create context with proper headers (like fixture-manager.ts)
  const context = await browser.newContext({
    extraHTTPHeaders: {
      Origin: process.env['BASE_URL'] || '',
      Referer: process.env['BASE_URL'] || '',
    },
  });
  const page = await context.newPage();
  setPage(page);

  try {
    // Login and save storage
    await signIn(username, password);
    await verifyLoginSuccess();
    await saveStorageState(finalPath);
    await closePage();

    logger.info(`[autoLogin] Successfully created and saved storage state: ${finalPath}`);
    return finalPath;
  } catch (error) {
    logger.error(`[autoLogin] Failed to create storage state:`, { value: error });

    try {
      await getPage().screenshot();
      await closePage();
    } catch (cleanupError) {
      logger.warn('[autoLogin] Cleanup failed', { value: cleanupError });
    }
    handleException(error, `Auto login failed for user: ${username}`);
  }
}

/**
 * Creates a storage state file using authentication context (mirrors fixture-manager.ts pattern).
 * This function follows the exact same pattern as the fixture manager for consistency.
 * @param browser - Browser instance
 * @param username - Username for login
 * @param password - Password for login
 * @param filePath - Full path to save storage state file
 * @param config - Optional configuration for auth context
 * @returns Promise<string> - The path to the saved storage state
 */
export async function createStorageStateWithAuthContext(
  browser: Browser,
  username: string,
  password: string,
  filePath: string,
  config: AuthContextConfig = {},
): Promise<string> {
  logger.info(`[createStorageStateWithAuthContext] Creating storage state: ${filePath}`);

  // Ensure directory exists
  ensureDirectoryExists(filePath);

  // Create context with proper headers for SPA tokens (same as fixture-manager.ts)
  const context = await createAuthContext(browser, config);
  const page = await context.newPage();
  setPage(page);

  try {
    await signIn(username, password);
    await verifyLoginSuccess();
    await saveStorageState(filePath);
    await closePage();

    logger.info(`[createStorageStateWithAuthContext] Successfully saved storage state: ${filePath}`);
    return filePath;
  } catch (error) {
    logger.error(`[createStorageStateWithAuthContext] Failed to create storage state:`, { value: error });

    try {
      await getPage().screenshot();
      await closePage();
    } catch (cleanupError) {
      logger.warn(`[createStorageStateWithAuthContext] Cleanup failed: `, { value: cleanupError });
    }

    handleException(error, `Failed to create storage state for user: ${username}`);
  }
}

/**
 * Resolves (and lazily creates) a storage state file with enhanced authentication context support.
 * This is the main function for handling storage state with proper auth context handling.
 *
 * Rules:
 *  - If neither storagePath nor workerId provided -> use current worker ID under <cwd>/.auth/<workerId>.json
 *  - If storagePath is a file (ends with .json) -> use it directly
 *  - If storagePath is a directory (exists or inferred) -> append <workerId>.json
 *  - If only workerId provided -> <cwd>/.auth/<workerId>.json
 *  - Reuse existing non-empty valid JSON file (skip login) unless reuseExisting is false
 *  - If browser is provided, use authentication context with proper headers
 *
 * @param username - Username for login
 * @param password - Password for login
 * @param options - Storage state options
 * @param browser - Optional browser instance for auth context
 * @param config - Optional configuration for auth context
 * @returns Promise<string> - Path to the storage state file
 */
export async function signInAndGetSessionState(
  username: string,
  password: string,
  options: StorageStateOptions = {},
  browser?: Browser,
  config: AuthContextConfig = {},
): Promise<string> {
  const { reuseExisting = true } = options;
  const finalPath = resolveStoragePath(options);
  const workerId = options.workerId ?? getCurrentWorkerId();

  logger.info(
    `[signInAndGetSessionState] Worker ID: ${workerId}, Resolving storage state for user: ${username}, path: ${finalPath}`,
  );

  // Reuse existing valid storage state if allowed
  if (reuseExisting && isValidStorageState(finalPath)) {
    logger.info(`[signInAndGetSessionState] Reusing existing storage state: ${finalPath}`);
    return finalPath;
  }

  logger.info(`[signInAndGetSessionState] Creating new storage state: ${finalPath}`);

  // Use auth context if browser is provided (recommended approach)
  if (browser) {
    return await createStorageStateWithAuthContext(browser, username, password, finalPath, config);
  }

  // Fallback to existing page context (backward compatibility)
  logger.warn('[signInAndGetSessionState] No browser provided, using existing page context (not recommended)');

  try {
    await signIn(username, password);

    ensureDirectoryExists(finalPath);
    await saveStorageState(finalPath);

    logger.info(`[signInAndGetSessionState] Saved storage state using existing context: ${finalPath}`);
    return finalPath;
  } catch (error) {
    logger.error(`[signInAndGetSessionState] Failed to create storage state`, { value: error });
    handleException(error, `Failed to create storage state for user: ${username}`);
  }
}

/**
 * Convenience function that mirrors the fixture-manager.ts authForRole pattern.
 * This can be used as a drop-in replacement for custom authentication scenarios.
 * @param browser - Browser instance
 * @param username - Username for login
 * @param password - Password for login
 * @param workerId - Worker ID for parallel execution (defaults to current worker)
 * @param baseAuthDir - Base directory for auth files (defaults to process.env.AUTH or '.auth')
 * @returns Promise<string> - Path to the created storage state
 */
export async function createAuthForUser(
  browser: Browser,
  username: string,
  password: string,
  workerId?: number,
  baseAuthDir?: string,
): Promise<string> {
  const effectiveWorkerId = workerId ?? getCurrentWorkerId();
  const authDir = baseAuthDir || process.env['AUTH_PATH'] || DEFAULT_AUTH_DIR;
  const filePath = path.resolve(authDir, `${effectiveWorkerId}.json`);

  return await createStorageStateWithAuthContext(browser, username, password, filePath);
}

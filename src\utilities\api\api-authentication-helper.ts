import { ApiHelper, type AuthConfig } from '@api-helper';
import { handleException, logger } from '@report-helper';

// ===============================================
// Authentication Configuration Functions
// ===============================================

/**
 * Configure Bearer token authentication
 */
export function configureBearerAuth(token: string): AuthConfig {
  try {
    logger.info(`[Auth Config] Configuring Bearer token authentication`);
    return {
      type: 'bearer',
      token,
    };
  } catch (error) {
    handleException(error, `[Auth Config] Failed to configure Bearer auth`);
  }
}

/**
 * Configure Basic authentication
 */
export function configureBasicAuth(username: string, password: string): AuthConfig {
  try {
    logger.info(`[Auth Config] Configuring Basic authentication for user: ${username}`);
    return {
      type: 'basic',
      username,
      password,
    };
  } catch (error) {
    handleException(error, `[Auth Config] Failed to configure Basic auth`);
  }
}

/**
 * Configure API Key authentication
 */
export function configureApiKeyAuth(apiKey: string, header: string = 'X-API-Key'): AuthConfig {
  try {
    logger.info(`[Auth Config] Configuring API Key authentication with header: ${header}`);
    return {
      type: 'apiKey',
      apiKey,
      apiKeyHeader: header,
    };
  } catch (error) {
    handleException(error, `[Auth Config] Failed to configure API Key auth`);
  }
}

/**
 * Configure OAuth2 client credentials authentication
 */
export function configureOAuth2Auth(tokenUrl: string, clientId: string, clientSecret: string): AuthConfig {
  try {
    logger.info(`[Auth Config] Configuring OAuth2 authentication with token URL: ${tokenUrl}`);
    return {
      type: 'oauth2',
      tokenUrl,
      clientId,
      clientSecret,
    };
  } catch (error) {
    handleException(error, `[Auth Config] Failed to configure OAuth2 auth`);
  }
}

// ===============================================
// Simplified Initialization Functions
// ===============================================

/**
 * Initialize API Helper with OAuth2 authentication
 */
export async function initializeWithOAuth2(
  baseURL: string,
  tokenUrl: string,
  clientId: string,
  clientSecret: string,
): Promise<void> {
  try {
    logger.info(`[Auth Init] Initializing with OAuth2 authentication`);
    const authConfig = configureOAuth2Auth(tokenUrl, clientId, clientSecret);
    await ApiHelper.initialize(baseURL, authConfig);
    logger.info('[Auth Init] OAuth2 initialization completed successfully');
  } catch (error) {
    handleException(error, `[Auth Init] OAuth2 initialization failed`);
  }
}

/**
 * Initialize API Helper with Bearer token authentication
 */
export async function initializeWithBearerToken(baseURL: string, token: string): Promise<void> {
  try {
    logger.info(`[Auth Init] Initializing with Bearer token authentication`);
    const authConfig = configureBearerAuth(token);
    await ApiHelper.initialize(baseURL, authConfig);
    logger.info('[Auth Init] Bearer token initialization completed successfully');
  } catch (error) {
    handleException(error, `[Auth Init] Bearer token initialization failed`);
  }
}

/**
 * Initialize API Helper with Basic authentication
 */
export async function initializeWithBasicAuth(baseURL: string, username: string, password: string): Promise<void> {
  try {
    logger.info(`[Auth Init] Initializing with Basic authentication for user: ${username}`);
    const authConfig = configureBasicAuth(username, password);
    await ApiHelper.initialize(baseURL, authConfig);
    logger.info('[Auth Init] Basic auth initialization completed successfully');
  } catch (error) {
    handleException(error, `[Auth Init] Basic auth initialization failed`);
  }
}

/**
 * Initialize API Helper with API Key authentication
 */
export async function initializeWithApiKey(baseURL: string, apiKey: string, header?: string): Promise<void> {
  try {
    logger.info(`[Auth Init] Initializing with API Key authentication`);
    const authConfig = configureApiKeyAuth(apiKey, header);
    await ApiHelper.initialize(baseURL, authConfig);
    logger.info('[Auth Init] API Key initialization completed successfully');
  } catch (error) {
    handleException(error, `[Auth Init] API Key initialization failed`);
  }
}

import { gotoURL } from '@page-utils';
import * as process from 'node:process';
import { button } from '@test-data/handlers/language-packs.handler';
import {
  click,
  expectElementToBeVisible,
  expectElementToHaveText,
  fill,
  getLocator,
  getLocatorByRole,
} from '@ui-action';
import { Locator } from '@playwright/test';

const baseUrl = process.env['BASE_URL'] || '';
const endpoint = 'logistics/purchase-orders';

export default class PurchaseOrderPage {
  private statusValue: Locator = getLocator('formcontrolname="statusDescription"');

  /**
   * Opens the Purchase Orders page via url.
   */
  async open(): Promise<void> {
    await gotoURL(`${baseUrl}/${endpoint}`);
  }

  /**
   * Opens the New Purchase Order page via url.
   */
  async openNewPurchaseOrder(): Promise<void> {
    await gotoURL(`${baseUrl}/${endpoint}/new`);
  }

  /**
   * Clicks the "Send to Approve" button and waits for the approval dialog to appear.
   */
  async clickSendToApprove() {
    const sendToApproveButton = getLocatorByRole('button', { name: button().sendToApprove });
    await click(sendToApproveButton);
    const dialog = getLocatorByRole('dialog').getByRole('heading', { name: button().sendToApprove });
    await expectElementToBeVisible(dialog);
  }

  /**
   * Clicks the "Approve" button and waits for the approval dialog to disappear.
   */
  async clickApprove() {
    const approveButton = getLocatorByRole('button', { name: button().approve });
    await click(approveButton);
  }

  /**
   * Selects an approval flow from the dropdown and clicks the "Send" button.
   * @param flowName
   */
  async selectApproveFlowThenSend(flowName: string) {
    const flowInput = getLocatorByRole('combobox');
    await fill(flowInput, flowName);
    const option = getLocatorByRole('option', { name: flowName });
    await click(option);
    const sendBtn = getLocatorByRole('button', { name: button().send });
    await click(sendBtn);
  }

  /**
   * Opens the Purchase Order Details page via url.
   * @param purchaseOrderId - The ID of the purchase order to open.
   */
  async openPurchaseOrderDetails(purchaseOrderId: string): Promise<void> {
    await gotoURL(`${baseUrl}/${endpoint}/${purchaseOrderId}`);
  }

  /**
   * Verifies that the purchase order status matches the expected status.
   * @param expectedStatus - The expected status text to verify.
   */
  async verifyPurchaseOrderStatus(expectedStatus: string): Promise<void> {
    await expectElementToHaveText(this.statusValue, expectedStatus);
  }

  /**
   * Checks if a table under a given tab has any items.
   * @param {string} tabName - The visible name of the tab to select.
   * @returns {Promise<boolean>} - True if table has data, false otherwise.
   */
  async isTableHasItem(tabName: string): Promise<boolean> {
    // Click the tab by its visible name
    await click(`text=${tabName}`);

    const table = getLocator('table');

    // Wait for the table to appear
    await expectElementToBeVisible(table);

    // Get all rows except the header
    const rows = await table.locator('tr').count();

    return rows > 0;
  }
}

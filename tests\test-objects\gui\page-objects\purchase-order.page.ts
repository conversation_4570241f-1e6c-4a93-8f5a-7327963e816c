import { getPage } from '@page-utils';
import * as process from 'node:process';
import { button } from '@test-data/handlers/language-packs.handler';
import { expectElementToHaveText } from '@ui-action';
import { Locator, Page } from '@playwright/test';

const baseUrl = process.env['BASE_URL'] || '';
const endpoint = 'logistics/purchase-orders';

export default class PurchaseOrderPage {
  private page: Page;
  private statusValue: Locator;

  constructor(page?: Page) {
    this.page = page || getPage();
    this.statusValue = this.page.locator('formcontrolname="statusDescription"');
  }

  /**
   * Opens the Purchase Orders page via url.
   */
  async open(): Promise<void> {
    await this.page.goto(`${baseUrl}/${endpoint}`);
  }

  /**
   * Opens the New Purchase Order page via url.
   */
  async openNewPurchaseOrder(): Promise<void> {
    await this.page.goto(`${baseUrl}/${endpoint}/new`);
  }

  /**
   * Clicks the "Send to Approve" button and waits for the approval dialog to appear.
   */
  async clickSendToApprove() {
    const sendToApproveButton = this.page.getByRole('button', { name: button().sendToApprove });
    await sendToApproveButton.click();
    const dialog = this.page.getByRole('dialog').getByRole('heading', { name: button().sendToApprove });
    await dialog.waitFor({ state: 'visible' });
  }

  /**
   * Clicks the "Approve" button and waits for the approval dialog to disappear.
   */
  async clickApprove() {
    const approveButton = this.page.getByRole('button', { name: button().approve });
    await approveButton.click();
  }

  /**
   * Selects an approval flow from the dropdown and clicks the "Send" button.
   * @param flowName
   */
  async selectApproveFlowThenSend(flowName: string) {
    const flowInput = this.page.getByRole('combobox');
    await flowInput.fill(flowName);
    const option = this.page.getByRole('option', { name: flowName });
    await option.click();
    const sendBtn = this.page.getByRole('button', { name: button().send });
    await sendBtn.click();
  }

  /**
   * Opens the Purchase Order Details page via url.
   * @param purchaseOrderId - The ID of the purchase order to open.
   */
  async openPurchaseOrderDetails(purchaseOrderId: string): Promise<void> {
    await this.page.goto(`${baseUrl}/${endpoint}/${purchaseOrderId}`);
  }

  /**
   * Verifies that the purchase order status matches the expected status.
   * @param expectedStatus - The expected status text to verify.
   */
  async verifyPurchaseOrderStatus(expectedStatus: string): Promise<void> {
    await expectElementToHaveText(this.statusValue, expectedStatus);
  }

  /**
   * Checks if a table under a given tab has any items.
   * @param {string} tabName - The visible name of the tab to select.
   * @returns {Promise<boolean>} - True if table has data, false otherwise.
   */
  async isTableHasItem(tabName: string): Promise<boolean> {
    // Click the tab by its visible name
    await this.page.click(`text=${tabName}`);

    const table = this.page.locator('table');

    // Wait for the table to appear
    await table.waitFor({ state: 'visible' });

    // Get all rows except the header
    const rows = await table.locator('tr').count();

    return rows > 0;
  }
}

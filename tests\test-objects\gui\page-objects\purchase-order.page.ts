import { getPage, gotoURL } from '@page-utils';
import * as process from 'node:process';
import { click, expectElementToBeVisible, getLocator } from '@ui-action';

const baseUrl = process.env['BASE_URL'] || '';
const endpoint = 'logistics/purchase-orders';

export default class PurchaseOrderPage {
  /**
   * Opens the Purchase Orders page via url.
   */
  async open(): Promise<void> {
    await gotoURL(`${baseUrl}/${endpoint}`);
  }

  /**
   * Opens the New Purchase Order page via url.
   */
  async openNewPurchaseOrder(): Promise<void> {
    await gotoURL(`${baseUrl}/${endpoint}/new`);
  }

  /**
   * Opens the Purchase Order Details page via url.
   * @param purchaseOrderId - The ID of the purchase order to open.
   */
  async openPurchaseOrderDetails(purchaseOrderId: string): Promise<void> {
    await gotoURL(`${baseUrl}/${endpoint}/${purchaseOrderId}`);
  }

  /**
   * Checks if a table under a given tab has any items.
   * @param {string} tabName - The visible name of the tab to select.
   * @returns {Promise<boolean>} - True if table has data, false otherwise.
   */
  async isTableHasItem(tabName: string): Promise<boolean> {
    // Click the tab by its visible name
    await click(`text=${tabName}`);

    const table = getLocator('table');

    // Wait for the table to appear
    await expectElementToBeVisible(table);

    // Get all rows except the header
    const rows = await table.locator('tr').count();

    return rows > 0;
  }
}

import { test, expect } from '@playwright/test';

// Sample API test spec

test.describe('API Tests', () => {
  test('should fetch user details successfully', async ({ request }) => {
    const response = await request.get('/api/users/1');
    expect(response.ok()).toBeTruthy();
    const responseBody = await response.json();

    expect(responseBody).toHaveProperty('id', 1);
    expect(responseBody).toHaveProperty('name');
    expect(responseBody).toHaveProperty('email');
  });

  test('should return 404 for non-existent user', async ({ request }) => {
    const response = await request.get('/api/users/9999');
    expect(response.status()).toBe(404);
  });

  test('should create a new user successfully', async ({ request }) => {
    const newUser = {
      name: '<PERSON>',
      email: '<EMAIL>',
    };

    const response = await request.post('/api/users', {
      data: newUser,
    });

    expect(response.status()).toBe(201);
    const responseBody = await response.json();

    expect(responseBody).toHaveProperty('id');
    expect(responseBody.name).toBe(newUser.name);
    expect(responseBody.email).toBe(newUser.email);
  });
});

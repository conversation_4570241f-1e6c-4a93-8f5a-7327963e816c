// tests/test-management/test-preparation/approve-flow.helper.ts

import process from 'node:process';
import { CONTEXT_COOKIE, createApiKeyAuth, get, getGlobalApiClient, post } from '@api-helper';
import { getCookie } from '@page-utils';
import { APIResponse } from '@playwright/test';
import { expect } from '@fixture-manager';
import { handleException } from '@report-helper';

export class ApproveFlowHelper {
  private readonly baseUrl: string;
  private readonly endPoint: string;

  constructor() {
    this.baseUrl = process.env['API_BASE_URL'] || '';
    this.endPoint = 'ApprovalFlow';
  }

  async init() {
    const apiKey = (await getCookie('BravoWeb')) || '';
    const client = getGlobalApiClient();
    await client.addContext(createApiKeyAuth(CONTEXT_COOKIE, `BravoWeb=${apiKey}`, { headerName: 'cookie' }));
  }

  async getApproveFlow(flowName: string): Promise<any> {
    try {
      const response = await get(`/${this.endPoint}/GetApprovalFlows`, {
        queryParams: {
          sord: 'asc',
          page: 1,
          rows: 1000,
        },
      });
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      const items: [] = data.rows || [];
      return items?.find((item: { FlowName: string }) => item.FlowName === flowName);
    } catch (error) {
      handleException(error, 'Error while retrieving get approve flows');
    }
  }

  async getStepByFlowId(flowId: string): Promise<any[]> {
    try {
      const response = await get(`/${this.endPoint}/GetStepsByApprovalFlowId`, {
        queryParams: {
          approvalFlowId: flowId,
          rows: 50,
          page: 1,
          sord: 'asc',
        },
      });
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      const items: [] = data.rows || [];
      return items;
    } catch (error) {
      handleException(error, 'Error while retrieving get approve steps');
    }
  }

  async createApproveFlow(flowName: string, description: string = ''): Promise<string> {
    try {
      const response = await post(`/${this.endPoint}/Edit`, {
        body: {
          ApprovalFlowId: '',
          Description: description,
          FlowName: flowName,
          PayrollAndExpense: false,
          TypeId: 1,
        },
      });
      expect(response.ok()).toBeTruthy();
      const data: any = await response.json();
      const id = data?.Data?.ApprovalFlowId;
      return id !== undefined ? String(id) : '';
    } catch (error) {
      handleException(error, 'Error while creating approve flows');
    }
  }

  async addApproveStep(flowId: string, personId: string): Promise<APIResponse> {
    try {
      const formData = new FormData();
      formData.append('ApprovalStepId', '');
      formData.append('ApprovalFlowId', flowId);
      formData.append('PersonId', personId);

      const response = await post(`/${this.endPoint}/EditApprovalStep`, { body: formData });
      expect(response.ok()).toBeTruthy();
      return response;
    } catch (error) {
      handleException(error, 'Error while adding approve step');
    }
  }
}

import { expect, test as baseTest } from '@global-config';
import path from 'path';
import { acquireAccount } from '@test-data/handlers/account-manager.handler';
import { handleException } from '@report-helper';
import { autoLogin } from '@components/login.func';
import { <PERSON><PERSON><PERSON> } from '@playwright/test';
import PurchaseOrderPage from '@page-objects/purchase-order.page';
import { ApproveFlowHelper } from '@test-data/approve-flow.helper';
import process from 'node:process';

function getFilePath(id: number): string {
  return path.resolve(process.env['AUTH'] || '.auth', process.env['NODE_ENV'] || '', `${id}.json`);
}

// Reusable helper to acquire / cache auth storage state for a role
async function authForRole(
  role: 'Admin' | 'Other',
  browser: Browser,
  use: (statePath: string) => Promise<void>,
  fileNameBuilder?: (id: number) => string,
) {
  const id = baseTest.info().parallelIndex;
  const fileName = fileNameBuilder ? fileNameBuilder(id) : getFilePath(id);

  try {
    const account = acquireAccount(role);

    // Use autoLogin which handles storage state checking and creation
    const storagePath: string = await autoLogin(
      browser,
      account.username,
      account.password || account.defaultPassword,
      {
        storagePath: fileName,
        workerId: id,
        reuseExisting: true,
      },
    );

    await use(storagePath);
  } catch (error) {
    handleException(error, `auto sign in with role (${role}) failed`);
  }
}

type PageObject = {
  purchaseOrder: PurchaseOrderPage;
  approveFlowHelper: ApproveFlowHelper;
};

const test = baseTest.extend<PageObject, { signInAsAdmin: string }>({
  // Use the same storage state for all tests in this worker.
  storageState: ({ signInAsAdmin }, use) => use(signInAsAdmin),

  signInAsAdmin: [
    async ({ browser }, use) => {
      // Preserve original filename pattern for Admin to keep existing cache
      await authForRole('Admin', browser, use, id => getFilePath(id));
    },
    { scope: 'worker' },
  ],
  // Page object fixture
  purchaseOrder: async ({}, use) => {
    await use(new PurchaseOrderPage());
  },
  approveFlowHelper: async ({}, use) => {
    const helper = new ApproveFlowHelper();
    await helper.init();
    await use(helper);
  },
});

export { test, expect };

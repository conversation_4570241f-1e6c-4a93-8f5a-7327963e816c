import { test as baseTest, expect } from '@global-config';
import path from 'path';
import { acquireAccount } from '@test-data/account-manager';
import { handleException } from '@report-helper';
import { autoLogin } from '@components/login.func';
import PurchaseOrderPage from '@page-objects/purchase-order.page';
import { Browser } from '@playwright/test';

function getFilePath(id: number): string {
  return path.resolve(process.env['AUTH_PATH'] || '.auth', `${id}.json`);
}

// Reusable helper to acquire / cache auth storage state for a role
async function authForRole(
  role: 'Admin' | 'Other',
  browser: Browser,
  use: (statePath: string) => Promise<void>,
  fileNameBuilder?: (id: number) => string,
) {
  const id = baseTest.info().parallelIndex;
  const fileName = fileNameBuilder ? fileNameBuilder(id) : getFilePath(id);

  try {
    const account = acquireAccount(role);

    // Use autoLogin which handles storage state checking and creation
    const storagePath: string = await autoLogin(
      browser,
      account.username,
      account.password || account.defaultPassword,
      {
        storagePath: fileName,
        workerId: id,
        reuseExisting: true,
      },
    );

    await use(storagePath);
  } catch (error) {
    handleException(error, `auto sign in with role (${role}) failed`);
  }
}

type PageObject = {
  purchaseOrderPage: PurchaseOrderPage;
};

const test = baseTest.extend<PageObject, { signInAsAdmin: string }>({
  // Use the same storage state for all tests in this worker.
  storageState: ({ signInAsAdmin }, use) => use(signInAsAdmin),

  signInAsAdmin: [
    async ({ browser }, use) => {
      // Preserve original filename pattern for Admin to keep existing cache
      await authForRole('Admin', browser, use, id => getFilePath(id));
    },
    { scope: 'worker' },
  ],
  // Page object fixture
  purchaseOrderPage: async ({}, use) => {
    await use(new PurchaseOrderPage());
  },
});

export { test, expect };
